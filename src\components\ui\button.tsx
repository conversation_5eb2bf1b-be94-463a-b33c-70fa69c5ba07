import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@/lib/utils";

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-xl text-sm font-medium shadow-sm transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        default:
          "bg-gradient-to-r from-blue-500 to-indigo-600 text-white hover:shadow-md hover:translate-y-[-1px] backdrop-blur-sm",
        destructive:
          "bg-gradient-to-r from-red-500 to-rose-600 text-white hover:shadow-lg hover:shadow-red-500/20 hover:translate-y-[-1px]",
        outline:
          "border border-slate-200/80 dark:border-slate-700/80 bg-white/80 dark:bg-slate-900/80 text-slate-900 dark:text-slate-100 hover:bg-slate-50 dark:hover:bg-slate-800 hover:shadow-md backdrop-blur-sm",
        secondary:
          "bg-slate-100/90 dark:bg-slate-800/90 text-slate-900 dark:text-slate-100 hover:bg-slate-200/90 dark:hover:bg-slate-700/90 hover:shadow-md backdrop-blur-sm",
        ghost:
          "hover:bg-slate-100/80 dark:hover:bg-slate-800/80 text-slate-900 dark:text-slate-100 backdrop-blur-sm",
        link: "text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline",
        premium:
          "bg-gradient-to-r from-purple-600 to-blue-600 text-white hover:shadow-lg hover:shadow-blue-500/25 hover:translate-y-[-2px] transition-all backdrop-blur-sm",
        glass:
          "bg-white/20 dark:bg-slate-900/30 backdrop-blur-xl border border-white/30 dark:border-slate-700/30 text-slate-900 dark:text-white hover:bg-white/30 dark:hover:bg-slate-800/40 hover:shadow-lg",
        glow: "bg-gradient-to-r from-purple-600 to-blue-600 text-white hover:shadow-lg hover:shadow-blue-500/40 hover:translate-y-[-2px] transition-all relative overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:animate-shimmer",
        primary:
          "bg-gradient-to-br from-blue-600 to-indigo-700 text-white hover:shadow-xl hover:shadow-blue-600/20 hover:translate-y-[-2px] transition-all backdrop-blur-sm",
      },
      size: {
        default: "h-10 px-5 py-2",
        sm: "h-9 rounded-lg px-3 text-xs",
        lg: "h-12 rounded-xl px-8 text-base",
        xl: "h-14 rounded-2xl px-10 text-lg font-semibold",
        icon: "h-10 w-10 rounded-full",
        pill: "h-10 px-6 rounded-full",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button";
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    );
  }
);
Button.displayName = "Button";

export { Button, buttonVariants };
