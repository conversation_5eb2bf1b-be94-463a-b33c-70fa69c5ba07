@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import react-draft-wysiwyg CSS for the editor */
@import 'react-draft-wysiwyg/dist/react-draft-wysiwyg.css';

/* Premium Design System - Modern UI/UX */

@layer base {
  :root {
    /* Core color palette - refined for premium look */
    --background: 210 40% 98%;
    --foreground: 222 47% 11%;

    /* Card colors with subtle depth */
    --card: 0 0% 100%;
    --card-foreground: 222 47% 11%;

    /* Popover with refined contrast */
    --popover: 0 0% 100%;
    --popover-foreground: 222 47% 11%;

    /* Brand colors - vibrant but sophisticated */
    --primary: 221 83% 53%;
    --primary-foreground: 210 40% 98%;

    /* Secondary colors - subtle and elegant */
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222 47% 11%;

    /* Muted tones for non-focal elements */
    --muted: 210 40% 96.1%;
    --muted-foreground: 215 16% 47%;

    /* Accent for highlights and focus states */
    --accent: 217 91% 60%;
    --accent-foreground: 210 40% 98%;

    /* Destructive with increased vibrance */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    /* Border and input styles */
    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 221 83% 53%;

    /* Premium UI elements */
    --radius: 0.85rem;

    /* Premium gradients */
    --gradient-primary: linear-gradient(
      135deg,
      hsl(221, 83%, 53%) 0%,
      hsl(239, 84%, 67%) 100%
    );
    --gradient-secondary: linear-gradient(
      135deg,
      hsl(210, 40%, 98%) 0%,
      hsl(214, 32%, 91%) 100%
    );
    --gradient-accent: linear-gradient(
      135deg,
      hsl(217, 91%, 60%) 0%,
      hsl(245, 58%, 51%) 100%
    );
    --gradient-destructive: linear-gradient(
      135deg,
      hsl(0, 84%, 60%) 0%,
      hsl(350, 89%, 60%) 100%
    );

    /* Glass effect variables */
    --glass-background: rgba(255, 255, 255, 0.8);
    --glass-border: rgba(255, 255, 255, 0.5);
    --glass-shadow: rgba(0, 0, 0, 0.05);

    /* Premium shadows */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.04), 0 1px 2px rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.05),
      0 2px 4px -1px rgba(0, 0, 0, 0.03);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.08),
      0 4px 6px -2px rgba(0, 0, 0, 0.03);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
      0 10px 10px -5px rgba(0, 0, 0, 0.02);
  }

  .dark {
    /* Dark mode - rich and sophisticated */
    --background: 222 47% 4%;
    --foreground: 210 40% 98%;

    --card: 222 47% 6%;
    --card-foreground: 210 40% 98%;

    --popover: 222 47% 6%;
    --popover-foreground: 210 40% 98%;

    /* Vibrant primary in dark mode */
    --primary: 217 91% 60%;
    --primary-foreground: 222 47% 4%;

    /* Rich secondary tones */
    --secondary: 217 19% 16%;
    --secondary-foreground: 210 40% 98%;

    /* Subtle muted tones */
    --muted: 217 19% 16%;
    --muted-foreground: 215 16% 65%;

    /* Accent with glow */
    --accent: 217 91% 60%;
    --accent-foreground: 210 40% 98%;

    /* Refined destructive for dark theme */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    /* Border and inputs with depth */
    --border: 217 19% 18%;
    --input: 217 19% 18%;
    --ring: 217 91% 60%;

    /* Dark mode glass effect */
    --glass-background: rgba(30, 41, 59, 0.7);
    --glass-border: rgba(71, 85, 105, 0.3);
    --glass-shadow: rgba(0, 0, 0, 0.1);

    /* Premium dark mode gradients */
    --gradient-primary: linear-gradient(
      135deg,
      hsl(217, 91%, 60%) 0%,
      hsl(239, 84%, 67%) 100%
    );
    --gradient-secondary: linear-gradient(
      135deg,
      hsl(217, 19%, 16%) 0%,
      hsl(222, 47%, 11%) 100%
    );
    --gradient-accent: linear-gradient(
      135deg,
      hsl(217, 91%, 60%) 0%,
      hsl(245, 58%, 51%) 100%
    );
    --gradient-destructive: linear-gradient(
      135deg,
      hsl(0, 84%, 60%) 0%,
      hsl(350, 89%, 60%) 100%
    );

    /* Premium shadows for dark mode */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.4), 0 1px 2px rgba(0, 0, 0, 0.6);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.5),
      0 2px 4px -1px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.8),
      0 4px 6px -2px rgba(0, 0, 0, 0.3);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.9),
      0 10px 10px -5px rgba(0, 0, 0, 0.5);
  }
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }

  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    font-feature-settings: "rlig" 1, "calt" 1;
    font-family: "Inter", system-ui, -apple-system, BlinkMacSystemFont,
      "Segoe UI", sans-serif;
  }
}

/* Premium Animation System */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% center;
  }
  100% {
    background-position: 200% center;
  }
}

@keyframes pulse-glow {
  0%,
  100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.8);
  }
}

@keyframes scale-in {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes bounce-gentle {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

/* Premium Utility Classes */
@layer components {
  .animate-fade-in {
    animation: fadeIn 0.6s ease-out;
  }

  .animate-slide-in-left {
    animation: slideInFromLeft 0.6s ease-out;
  }

  .animate-slide-in-right {
    animation: slideInFromRight 0.6s ease-out;
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-shimmer {
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.4),
      transparent
    );
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }

  .animate-pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite;
  }

  .animate-scale-in {
    animation: scale-in 0.3s ease-out;
  }

  .animate-bounce-gentle {
    animation: bounce-gentle 2s ease-in-out infinite;
  }

  /* Premium Gradient Backgrounds */
  .bg-gradient-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  }

  .bg-gradient-secondary {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  }

  .bg-gradient-accent {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  }

  .bg-gradient-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .bg-gradient-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  }

  /* Glassmorphism Effects */
  .glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .glass-dark {
    background: rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .glass-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
  }

  /* Premium Shadow Effects */
  .shadow-premium {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  }

  .shadow-glow {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }

  .shadow-glow-purple {
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
  }

  /* Interactive States */
  .interactive {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .interactive:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  }

  .interactive-scale:hover {
    transform: scale(1.05);
  }

  .interactive-glow:hover {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.4);
  }

  /* Typography */
  .text-gradient {
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .text-hero {
    font-size: 2.25rem;
    line-height: 1.2;
    font-weight: 700;
  }

  @media (min-width: 768px) {
    .text-hero {
      font-size: 3.75rem;
    }
  }

  @media (min-width: 1024px) {
    .text-hero {
      font-size: 4.5rem;
    }
  }

  .text-section-title {
    font-size: 1.5rem;
    font-weight: 700;
  }

  @media (min-width: 768px) {
    .text-section-title {
      font-size: 1.875rem;
    }
  }

  @media (min-width: 1024px) {
    .text-section-title {
      font-size: 2.25rem;
    }
  }

  .text-card-title {
    font-size: 1.25rem;
    font-weight: 600;
  }

  @media (min-width: 768px) {
    .text-card-title {
      font-size: 1.5rem;
    }
  }

  /* Buttons */
  .btn-premium {
    padding: 1rem 2rem;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    font-weight: 600;
    border-radius: 0.5rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
      0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 300ms;
  }

  .btn-premium:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
      0 10px 10px -5px rgba(0, 0, 0, 0.04);
    transform: translateY(-2px) scale(1.05);
  }

  .btn-secondary {
    padding: 0.75rem 1.5rem;
    background-color: white;
    color: #1f2937;
    font-weight: 500;
    border-radius: 0.5rem;
    border: 2px solid #e5e7eb;
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 300ms;
  }

  .btn-secondary:hover {
    border-color: #3b82f6;
    color: #2563eb;
  }

  .btn-ghost {
    padding: 0.75rem 1.5rem;
    color: #4b5563;
    font-weight: 500;
    border-radius: 0.5rem;
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 300ms;
  }

  .btn-ghost:hover {
    background-color: #f3f4f6;
  }

  /* Cards */
  .card-premium {
    background-color: white;
    border-radius: 0.75rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
      0 4px 6px -2px rgba(0, 0, 0, 0.05);
    border: 1px solid #f3f4f6;
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 300ms;
  }

  .card-premium:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
      0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  .card-featured {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 0.75rem;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 500ms;
  }

  .card-featured:hover {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }

  .card-glass {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 0.75rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
      0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 300ms;
  }

  .card-glass:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
      0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  /* Layouts */
  .container-premium {
    max-width: 80rem;
    margin-left: auto;
    margin-right: auto;
    padding-left: 1rem;
    padding-right: 1rem;
  }

  @media (min-width: 640px) {
    .container-premium {
      padding-left: 1.5rem;
      padding-right: 1.5rem;
    }
  }

  @media (min-width: 1024px) {
    .container-premium {
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }

  .section-padding {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }

  @media (min-width: 768px) {
    .section-padding {
      padding-top: 6rem;
      padding-bottom: 6rem;
    }
  }

  @media (min-width: 1024px) {
    .section-padding {
      padding-top: 8rem;
      padding-bottom: 8rem;
    }
  }

  .section-padding-sm {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }

  @media (min-width: 768px) {
    .section-padding-sm {
      padding-top: 3rem;
      padding-bottom: 3rem;
    }
  }

  @media (min-width: 1024px) {
    .section-padding-sm {
      padding-top: 4rem;
      padding-bottom: 4rem;
    }
  }
}

/* Scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
