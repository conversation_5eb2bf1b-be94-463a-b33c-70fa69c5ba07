SCHEME OF WORK - PRE-TECHNICAL STUDIES
GRADE 9 - TERM 1

WEEK 1: Introduction to Pre-Technical Studies
Strand: Technology and Society
Sub-strand: Understanding Technology
Learning Outcomes:
- Students will define technology and its role in society
- Students will identify different types of technology
Key Inquiry Question: How does technology impact our daily lives?
Learning Experiences:
- Discussion on technology in daily life
- Identification of technological devices
Learning Resources:
- Textbooks, visual aids, real objects
Assessment: Observation and questioning
PCIs: Life skills, environmental awareness
Values: Responsibility, respect for technology
Core Competencies: Critical thinking, communication

WEEK 2: Basic Tools and Equipment
Strand: Tools and Materials
Sub-strand: Hand Tools
Learning Outcomes:
- Students will identify common hand tools
- Students will demonstrate safe use of basic tools
Key Inquiry Question: Why is tool safety important?
Learning Experiences:
- Tool identification activity
- Safety demonstration
Learning Resources:
- Hand tools, safety equipment
Assessment: Practical demonstration
PCIs: Safety awareness, life skills
Values: Responsibility, safety consciousness
Core Competencies: Problem solving, creativity

WEEK 3: Materials and Their Properties
Strand: Materials Science
Sub-strand: Material Properties
Learning Outcomes:
- Students will classify different materials
- Students will describe material properties
Key Inquiry Question: How do material properties affect their use?
Learning Experiences:
- Material testing activities
- Property comparison
Learning Resources:
- Various materials, testing equipment
Assessment: Practical testing and recording
PCIs: Environmental awareness, resource management
Values: Curiosity, respect for resources
Core Competencies: Critical thinking, creativity

WEEK 4: Simple Mechanisms
Strand: Mechanical Systems
Sub-strand: Basic Mechanisms
Learning Outcomes:
- Students will identify simple machines
- Students will explain how levers work
Key Inquiry Question: How do simple machines make work easier?
Learning Experiences:
- Building simple levers
- Testing mechanical advantage
Learning Resources:
- Construction materials, measuring tools
Assessment: Model construction and explanation
PCIs: Innovation, problem solving
Values: Persistence, creativity
Core Competencies: Problem solving, communication
