#!/usr/bin/env python3
"""
Enterprise-Grade AI Processor for Large CBC Scheme Documents
Handles 15+ weeks with 120+ lessons through intelligent chunking
"""

import asyncio
import aiohttp
import json
import re
import time
import logging
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from performance_monitor import monitor_performance, track_component_time

@dataclass
class ProcessingProgress:
    total_weeks: int
    completed_weeks: int
    current_week: int
    total_lessons: int
    completed_lessons: int
    estimated_time_remaining: float
    status: str

class EnterpriseAIProcessor:
    """
    Enterprise-grade AI processor for large CBC scheme documents
    Features:
    - Chunked processing for documents with 100+ lessons
    - Week-by-week intelligent splitting
    - Progress tracking and real-time updates
    - Teacher customization options
    - Robust error handling and recovery
    """
    
    def __init__(self):
        self.api_key = "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"
        self.base_url = "https://openrouter.ai/api/v1/chat/completions"
        self.logger = logging.getLogger(__name__)
        
        # FREE models optimized for educational content
        self.models = [
            "deepseek/deepseek-chat:free",           # Primary: Best for structured content
            "meta-llama/llama-3.3-70b-instruct:free", # Fallback: Large context
            "deepseek/deepseek-r1:free"              # Fallback: Reasoning focused
        ]
        
        # Chunking configuration for large documents
        self.max_chunk_size = 8000  # Characters per chunk (safe for free models)
        self.overlap_size = 500     # Overlap between chunks to maintain context
        self.max_concurrent_chunks = 2  # Process 2 chunks simultaneously
        
        # Session management
        self.session = None
        self.request_timeout = 60  # Increased for complex processing
        self.semaphore = asyncio.Semaphore(self.max_concurrent_chunks)
        
        # Progress tracking
        self.progress_callback = None
        
    async def initialize(self):
        """Initialize HTTP session for API calls"""
        if not self.session:
            timeout = aiohttp.ClientTimeout(total=self.request_timeout)
            connector = aiohttp.TCPConnector(
                limit=10,
                limit_per_host=5,
                ttl_dns_cache=300,
                use_dns_cache=True,
            )
            self.session = aiohttp.ClientSession(
                timeout=timeout,
                connector=connector,
                headers={
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json",
                    "HTTP-Referer": "https://teach-easy-convert.com",
                    "X-Title": "CBC Enterprise Lesson Plan Generator",
                }
            )
    
    async def close(self):
        """Clean up resources"""
        if self.session:
            await self.session.close()
    
    def set_progress_callback(self, callback):
        """Set callback function for progress updates"""
        self.progress_callback = callback
    
    def _update_progress(self, progress: ProcessingProgress):
        """Update processing progress"""
        if self.progress_callback:
            self.progress_callback(progress)
        
        # Log progress
        percentage = (progress.completed_weeks / progress.total_weeks) * 100 if progress.total_weeks > 0 else 0
        self.logger.info(f"Progress: {percentage:.1f}% - Week {progress.current_week}/{progress.total_weeks} - {progress.status}")
    
    def _extract_weeks_from_content(self, content: str) -> List[Dict]:
        """
        Intelligently extract week-by-week content from scheme document
        Returns list of week chunks with metadata
        """
        weeks = []
        
        # Enhanced patterns to detect week boundaries
        week_patterns = [
            r'(?i)week\s*(\d+)',
            r'(?i)wk\s*(\d+)',
            r'(?i)w(\d+)',
            r'(\d+)\s*(?:st|nd|rd|th)?\s*week',
        ]
        
        # Split content into lines for analysis
        lines = content.split('\n')
        current_week = None
        current_content = []
        week_number = 1
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # Check if this line indicates a new week
            week_found = False
            for pattern in week_patterns:
                match = re.search(pattern, line)
                if match:
                    # Save previous week if exists
                    if current_week is not None and current_content:
                        weeks.append({
                            'week_number': current_week,
                            'content': '\n'.join(current_content),
                            'line_count': len(current_content),
                            'char_count': len('\n'.join(current_content))
                        })
                    
                    # Start new week
                    current_week = int(match.group(1)) if match.group(1).isdigit() else week_number
                    current_content = [line]
                    week_found = True
                    week_number = max(week_number, current_week) + 1
                    break
            
            if not week_found and current_week is not None:
                current_content.append(line)
        
        # Add final week
        if current_week is not None and current_content:
            weeks.append({
                'week_number': current_week,
                'content': '\n'.join(current_content),
                'line_count': len(current_content),
                'char_count': len('\n'.join(current_content))
            })
        
        # If no weeks detected, create chunks based on content size
        if not weeks:
            weeks = self._create_content_chunks(content)
        
        return weeks
    
    def _create_content_chunks(self, content: str) -> List[Dict]:
        """Create content chunks when week detection fails"""
        chunks = []
        chunk_size = self.max_chunk_size
        overlap = self.overlap_size
        
        for i in range(0, len(content), chunk_size - overlap):
            chunk_content = content[i:i + chunk_size]
            chunks.append({
                'week_number': len(chunks) + 1,
                'content': chunk_content,
                'line_count': len(chunk_content.split('\n')),
                'char_count': len(chunk_content),
                'is_chunk': True
            })
        
        return chunks
    
    def _create_week_prompt(self, week_content: str, week_number: int, teacher_preferences: Dict = None) -> str:
        """Create optimized prompt for processing a single week"""
        
        # Default teacher preferences
        prefs = teacher_preferences or {}
        school_name = prefs.get('school_name', 'CBC School')
        grade_level = prefs.get('grade_level', 'Grade 5')
        subject = prefs.get('subject', 'Science Technology')
        term = prefs.get('term', 'Term 2')
        
        return f"""You are an expert CBC curriculum specialist. Extract ALL lesson plans from this Week {week_number} content.

TEACHER PREFERENCES:
- School: {school_name}
- Grade: {grade_level}  
- Subject: {subject}
- Term: {term}

WEEK {week_number} CONTENT:
{week_content}

EXTRACT ALL LESSONS from this week. Return a JSON array with detailed lesson plans:

[{{
  "week": {week_number},
  "lessonNumber": 1,
  "school": "{school_name}",
  "level": "{grade_level}",
  "learning_area": "{subject}",
  "term": "{term}",
  "subject": "{subject}",
  "strand": "Main topic from content",
  "sub_strand": "Specific subtopic",
  "specific_learning_outcomes": ["By the end of the lesson learner should be able to...", "Second outcome..."],
  "key_inquiry_question": "Question from the document",
  "learning_experiences": ["Activity 1", "Activity 2"],
  "learning_resources": ["Resource 1", "Resource 2"],
  "assessment": "Assessment method",
  "pcis": ["PCI 1", "PCI 2"],
  "values": ["Value 1", "Value 2"],
  "core_competencies": ["Competency 1", "Competency 2"],
  "date": "2025-07-03",
  "time": "40 minutes"
}}]

INSTRUCTIONS:
- Extract EVERY lesson from this week's content
- Use exact text from the document where possible
- If multiple lessons exist in this week, create separate entries
- Ensure all JSON is valid
- Return ONLY the JSON array"""

    async def _process_week_chunk(self, week_data: Dict, teacher_preferences: Dict = None) -> List[Dict]:
        """Process a single week chunk with AI"""
        week_number = week_data['week_number']
        content = week_data['content']
        
        prompt = self._create_week_prompt(content, week_number, teacher_preferences)
        
        async with self.semaphore:
            for model in self.models:
                try:
                    await self.initialize()
                    
                    payload = {
                        "model": model,
                        "messages": [
                            {
                                "role": "system",
                                "content": "You are an expert CBC curriculum specialist. Extract detailed lesson plans from scheme of work content. Always respond with valid JSON arrays containing complete lesson plan data."
                            },
                            {
                                "role": "user",
                                "content": prompt
                            }
                        ],
                        "max_tokens": 4000,
                        "temperature": 0.1,
                        "stream": False
                    }
                    
                    async with self.session.post(self.base_url, json=payload) as response:
                        if response.status == 200:
                            result = await response.json()
                            ai_response = result.get('choices', [{}])[0].get('message', {}).get('content', '')
                            
                            # Parse JSON response
                            lessons = self._parse_ai_response(ai_response)
                            if lessons:
                                self.logger.info(f"✅ Week {week_number}: Extracted {len(lessons)} lessons with {model}")
                                return lessons
                        else:
                            self.logger.warning(f"❌ Week {week_number}: API error {response.status} for {model}")
                            
                except Exception as e:
                    self.logger.error(f"❌ Week {week_number}: Error with {model}: {e}")
                    continue
        
        # Fallback: Create basic lesson structure
        self.logger.warning(f"⚠️ Week {week_number}: Using fallback processing")
        return [{
            "week": week_number,
            "lessonNumber": 1,
            "school": teacher_preferences.get('school_name', 'CBC School') if teacher_preferences else 'CBC School',
            "level": teacher_preferences.get('grade_level', 'Grade 5') if teacher_preferences else 'Grade 5',
            "learning_area": teacher_preferences.get('subject', 'Science Technology') if teacher_preferences else 'Science Technology',
            "strand": f"Week {week_number} Content",
            "sub_strand": "Extracted from scheme",
            "specific_learning_outcomes": [f"Week {week_number} learning objectives"],
            "key_inquiry_question": f"What are the key concepts in Week {week_number}?",
            "learning_experiences": ["Content analysis", "Practical activities"],
            "learning_resources": ["Scheme content", "Learning materials"],
            "assessment": "Continuous assessment",
            "date": "2025-07-03",
            "time": "40 minutes",
            "term": teacher_preferences.get('term', 'Term 2') if teacher_preferences else 'Term 2',
            "subject": teacher_preferences.get('subject', 'Science Technology') if teacher_preferences else 'Science Technology'
        }]
    
    def _parse_ai_response(self, response: str) -> Optional[List[Dict]]:
        """Parse AI response and extract lesson plans"""
        try:
            # Clean response
            response = response.strip()
            if response.startswith('```json'):
                response = response[7:]
            if response.endswith('```'):
                response = response[:-3]
            response = response.strip()
            
            # Parse JSON
            data = json.loads(response)
            if isinstance(data, list) and len(data) > 0:
                return data
            elif isinstance(data, dict):
                return [data]  # Single lesson
                
        except json.JSONDecodeError as e:
            self.logger.error(f"JSON parsing error: {e}")
        except Exception as e:
            self.logger.error(f"Response parsing error: {e}")
            
        return None

    async def process_large_scheme(self, content: str, filename: str, teacher_preferences: Dict = None) -> Dict:
        """
        Process large scheme documents with enterprise-grade chunking
        Handles 15+ weeks with 120+ lessons
        """
        start_time = time.time()

        try:
            # Extract weeks from content
            print("🔍 Analyzing scheme structure...")
            weeks = self._extract_weeks_from_content(content)

            if not weeks:
                return {
                    "success": False,
                    "message": "❌ Could not extract week structure from document",
                    "lesson_plans": [],
                    "processing_time": time.time() - start_time
                }

            print(f"📊 Found {len(weeks)} weeks to process")
            for i, week in enumerate(weeks[:5]):  # Show first 5 weeks
                print(f"   Week {week['week_number']}: {week['char_count']} chars, {week['line_count']} lines")
            if len(weeks) > 5:
                print(f"   ... and {len(weeks) - 5} more weeks")

            # Initialize progress tracking
            total_lessons_estimate = len(weeks) * 4  # Estimate 4 lessons per week
            progress = ProcessingProgress(
                total_weeks=len(weeks),
                completed_weeks=0,
                current_week=1,
                total_lessons=total_lessons_estimate,
                completed_lessons=0,
                estimated_time_remaining=len(weeks) * 15,  # 15 seconds per week estimate
                status="Starting processing..."
            )
            self._update_progress(progress)

            # Process weeks in batches
            all_lessons = []
            batch_size = 3  # Process 3 weeks at a time

            for batch_start in range(0, len(weeks), batch_size):
                batch_end = min(batch_start + batch_size, len(weeks))
                batch_weeks = weeks[batch_start:batch_end]

                print(f"\n🔄 Processing batch: Weeks {batch_weeks[0]['week_number']}-{batch_weeks[-1]['week_number']}")

                # Process batch concurrently
                batch_tasks = []
                for week_data in batch_weeks:
                    task = self._process_week_chunk(week_data, teacher_preferences)
                    batch_tasks.append(task)

                # Wait for batch completion
                batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)

                # Process results
                for i, result in enumerate(batch_results):
                    week_data = batch_weeks[i]
                    week_num = week_data['week_number']

                    if isinstance(result, Exception):
                        print(f"   ❌ Week {week_num}: Error - {result}")
                        # Add fallback lesson
                        all_lessons.append({
                            "week": week_num,
                            "lessonNumber": 1,
                            "error": str(result),
                            "status": "failed"
                        })
                    elif isinstance(result, list) and len(result) > 0:
                        print(f"   ✅ Week {week_num}: {len(result)} lessons extracted")
                        all_lessons.extend(result)
                    else:
                        print(f"   ⚠️ Week {week_num}: No lessons extracted")

                # Update progress
                progress.completed_weeks = batch_end
                progress.current_week = batch_end + 1 if batch_end < len(weeks) else len(weeks)
                progress.completed_lessons = len(all_lessons)
                progress.estimated_time_remaining = (len(weeks) - batch_end) * 15
                progress.status = f"Processed {batch_end}/{len(weeks)} weeks"
                self._update_progress(progress)

                # Brief pause between batches to avoid rate limiting
                if batch_end < len(weeks):
                    await asyncio.sleep(2)

            # Final processing
            processing_time = time.time() - start_time
            successful_lessons = [l for l in all_lessons if not l.get('error')]
            failed_weeks = len([l for l in all_lessons if l.get('error')])

            # Final progress update
            progress.status = "Complete"
            progress.estimated_time_remaining = 0
            self._update_progress(progress)

            print(f"\n🎉 PROCESSING COMPLETE!")
            print(f"   📊 Total lessons: {len(successful_lessons)}")
            print(f"   ⏱️ Processing time: {processing_time:.1f} seconds")
            print(f"   📈 Rate: {len(successful_lessons)/processing_time:.1f} lessons/second")
            if failed_weeks > 0:
                print(f"   ⚠️ Failed weeks: {failed_weeks}")

            return {
                "success": True,
                "message": f"🎓 Enterprise processing complete - {len(successful_lessons)} lessons from {len(weeks)} weeks",
                "lesson_plans": successful_lessons,
                "processing_method": "ENTERPRISE_CHUNKED_AI",
                "weeks_processed": len(weeks),
                "weeks_found": [w['week_number'] for w in weeks],
                "failed_weeks": failed_weeks,
                "processing_time": processing_time,
                "lessons_per_second": len(successful_lessons)/processing_time if processing_time > 0 else 0,
                "teacher_preferences": teacher_preferences or {}
            }

        except Exception as e:
            self.logger.error(f"Enterprise processing error: {e}")
            return {
                "success": False,
                "message": f"❌ Enterprise processing failed: {str(e)}",
                "lesson_plans": [],
                "processing_time": time.time() - start_time,
                "error": str(e)
            }
