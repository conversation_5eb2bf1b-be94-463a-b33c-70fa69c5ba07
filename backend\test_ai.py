#!/usr/bin/env python3

import asyncio
import sys
import os
sys.path.append('.')

from optimized_ai_processor import OptimizedAIProcessor

async def test_ai_processing():
    print('🧪 Testing DeepSeek V3 Free AI Processing with FULL STM2025.pdf...')

    from optimized_file_processor import OptimizedFileProcessor

    ai_processor = OptimizedAIProcessor()
    file_processor = OptimizedFileProcessor()

    # Extract full content from STM2025.pdf
    print('📄 Reading STM2025.pdf...')
    with open('../STM2025.pdf', 'rb') as f:
        file_content = f.read()

    test_content, metadata = file_processor.extract_text_fast(file_content, 'STM2025.pdf', 'full_test')
    print(f'📄 PDF extraction: {metadata}')
    print(f'📄 First 500 characters: {test_content[:500]}...')
    
    try:
        print(f'📝 Content length: {len(test_content)} characters')
        print(f'🤖 Using models: {ai_processor.fast_models}')
        print(f'🔑 API Key: {ai_processor.api_key[:20]}...')
        
        print('\n⏳ Processing FULL PDF with AI (may take 60-90 seconds)...')
        result = await ai_processor.process_scheme_fast(test_content, 'STM2025.pdf')
        
        print(f'\n📊 RESULT:')
        if result['success']:
            print(f'✅ SUCCESS: {result["message"]}')
            print(f'🔧 Processing method: {result.get("processing_method", "Unknown")}')
            print(f'📅 Weeks found: {result.get("weeks_found", [])}')
            print(f'📚 Total lesson plans: {len(result.get("lesson_plans", []))}')
            
            if result.get('lesson_plans'):
                print(f'\n📖 First lesson plan:')
                lesson = result['lesson_plans'][0]
                print(f'   Week: {lesson.get("week")}')
                print(f'   Strand: {lesson.get("strand")}')
                print(f'   Sub-strand: {lesson.get("sub_strand")}')
                print(f'   Learning Area: {lesson.get("learning_area")}')
                print(f'   Outcomes: {lesson.get("specific_learning_outcomes", [])}')
        else:
            print(f'❌ FAILED: {result["message"]}')
            
        await ai_processor.close()
        print('\n🏁 Test completed!')
        
    except Exception as e:
        print(f'💥 Error: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_ai_processing())
