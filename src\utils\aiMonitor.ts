/**
 * AI-Powered CBC Lesson Plan Generator - Enhanced Monitoring
 * Real-time AI performance tracking and quality assurance
 */

export class AIMonitor {
  private static instance: AIMonitor;
  private processingQueue: Map<string, any> = new Map();
  private performanceMetrics: any = {
    totalRequests: 0,
    successfulParses: 0,
    aiEnsembleUses: 0,
    averageProcessingTime: 0,
    qualityScores: [],
  };

  static getInstance(): AIMonitor {
    if (!AIMonitor.instance) {
      AIMonitor.instance = new AIMonitor();
    }
    return AIMonitor.instance;
  }

  trackProcessingStart(id: string, method: string) {
    this.processingQueue.set(id, {
      startTime: Date.now(),
      method,
      status: "processing",
    });
    this.performanceMetrics.totalRequests++;
  }

  trackProcessingComplete(id: string, result: any) {
    const processing = this.processingQueue.get(id);
    if (processing) {
      const processingTime = Date.now() - processing.startTime;

      // Update metrics
      if (result.success) {
        this.performanceMetrics.successfulParses++;
      }

      if (result.processing_method?.includes("AI")) {
        this.performanceMetrics.aiEnsembleUses++;
      }

      // Calculate average processing time
      const currentAvg = this.performanceMetrics.averageProcessingTime;
      const totalRequests = this.performanceMetrics.totalRequests;
      this.performanceMetrics.averageProcessingTime =
        (currentAvg * (totalRequests - 1) + processingTime) / totalRequests;

      // Track quality scores
      if (result.accuracy_score) {
        this.performanceMetrics.qualityScores.push(result.accuracy_score);
        // Keep only last 100 scores
        if (this.performanceMetrics.qualityScores.length > 100) {
          this.performanceMetrics.qualityScores.shift();
        }
      }

      this.processingQueue.delete(id);
    }
  }

  getMetrics() {
    const qualityScores = this.performanceMetrics.qualityScores;
    const averageQuality =
      qualityScores.length > 0
        ? qualityScores.reduce((a: number, b: number) => a + b, 0) /
          qualityScores.length
        : 0;

    return {
      ...this.performanceMetrics,
      successRate:
        this.performanceMetrics.totalRequests > 0
          ? (this.performanceMetrics.successfulParses /
              this.performanceMetrics.totalRequests) *
            100
          : 0,
      aiUsageRate:
        this.performanceMetrics.totalRequests > 0
          ? (this.performanceMetrics.aiEnsembleUses /
              this.performanceMetrics.totalRequests) *
            100
          : 0,
      averageQualityScore: averageQuality,
      currentlyProcessing: this.processingQueue.size,
    };
  }

  generateProcessingId(): string {
    return `proc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

export const aiMonitor = AIMonitor.getInstance();
