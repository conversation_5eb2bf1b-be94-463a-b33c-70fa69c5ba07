"""
Optimized AI Processing System
Fixes performance bottlenecks and reduces processing time from 20 minutes to under 30 seconds
"""
import asyncio
import aiohttp
import json
import time
import logging
from typing import Dict, List, Optional, Tuple
from datetime import datetime
import hashlib
import re
from performance_monitor import performance_monitor, monitor_performance, track_component_time

class OptimizedAIProcessor:
    """
    High-performance AI processor with aggressive optimizations
    """
    
    def __init__(self):
        self.api_key = "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"
        self.base_url = "https://openrouter.ai/api/v1/chat/completions"
        self.logger = logging.getLogger(__name__)
        
        # FREE model selection - completely free models from OpenRouter
        # Prioritize MoE models for efficiency (like Qwen 3.5's 235B-A22B architecture)
        self.fast_models = [
            "qwen/qwen3-235b-a22b:free",             # Primary: Qwen 3 MoE - 235B total, 22B active, 128K context
            "deepseek/deepseek-chat:free",           # Fallback: DeepSeek V3 (Free) - 164K context
            "meta-llama/llama-3.3-70b-instruct:free", # Fallback: Llama 3.3 70B (Free) - 131K context
            "deepseek/deepseek-r1:free"              # Fallback: DeepSeek R1 (Free) - 164K context
        ]
        
        # Cache for AI responses
        self.response_cache = {}
        self.cache_ttl = 3600  # 1 hour cache
        
        # Connection pool for HTTP requests
        self.session = None
        
        # Performance settings
        self.max_content_length = 50000  # Increased limit for free models with large context (164K tokens)
        self.request_timeout = 120  # INCREASED: 120 second timeout for MoE processing
        self.max_concurrent_requests = 3  # Limit concurrent AI calls
        self.semaphore = asyncio.Semaphore(self.max_concurrent_requests)
        
    async def initialize(self):
        """Initialize HTTP session with optimized settings"""
        if not self.session:
            timeout = aiohttp.ClientTimeout(total=self.request_timeout)
            connector = aiohttp.TCPConnector(
                limit=10,  # Connection pool size
                limit_per_host=5,
                ttl_dns_cache=300,
                use_dns_cache=True,
            )
            self.session = aiohttp.ClientSession(
                timeout=timeout,
                connector=connector,
                headers={
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json",
                    "HTTP-Referer": "https://teach-easy-convert.com",
                    "X-Title": "CBC Lesson Plan Generator",
                }
            )
    
    async def close(self):
        """Clean up resources"""
        if self.session:
            await self.session.close()
            
    def _get_cache_key(self, content: str, model: str) -> str:
        """Generate cache key for content"""
        content_hash = hashlib.md5(content.encode()).hexdigest()
        return f"{model}_{content_hash}"
        
    def _is_cache_valid(self, cache_entry: Dict) -> bool:
        """Check if cache entry is still valid"""
        return (time.time() - cache_entry["timestamp"]) < self.cache_ttl
        
    def _preprocess_content(self, content: str) -> str:
        """Optimize content for faster processing"""
        # Remove excessive whitespace
        content = re.sub(r'\s+', ' ', content.strip())
        
        # Truncate if too long
        if len(content) > self.max_content_length:
            # Try to find a good breaking point
            sentences = content.split('.')
            truncated = ""
            for sentence in sentences:
                if len(truncated + sentence) > self.max_content_length:
                    break
                truncated += sentence + "."
            content = truncated if truncated else content[:self.max_content_length]
            
        return content
        
    async def _make_ai_request(self, content: str, model: str, request_id: str) -> Optional[Dict]:
        """Make optimized AI request with caching and error handling"""
        # Check cache first
        cache_key = self._get_cache_key(content, model)
        if cache_key in self.response_cache:
            cache_entry = self.response_cache[cache_key]
            if self._is_cache_valid(cache_entry):
                with track_component_time(request_id, f"cache_hit_{model}"):
                    return cache_entry["data"]
        
        # Preprocess content
        processed_content = self._preprocess_content(content)
        
        # Create optimized prompt
        prompt = self._create_optimized_prompt(processed_content)
        
        async with self.semaphore:  # Limit concurrent requests
            with track_component_time(request_id, f"ai_request_{model}"):
                try:
                    await self.initialize()
                    
                    payload = {
                        "model": model,
                        "messages": [
                            {
                                "role": "system",
                                "content": "You are an expert CBC (Competency-Based Curriculum) education specialist for Kenya. Extract and structure lesson plans from scheme of work documents. Always respond with valid JSON containing detailed lesson plans with proper CBC format including strands, sub-strands, learning outcomes, key inquiry questions, learning experiences, resources, and assessment methods."
                            },
                            {
                                "role": "user",
                                "content": prompt
                            }
                        ],
                        "max_tokens": 4000,  # Increased for detailed lesson plans (free models)
                        "temperature": 0.1,
                        "stream": False
                    }

                    headers = {
                        "Authorization": f"Bearer {self.api_key}",
                        "Content-Type": "application/json",
                        "HTTP-Referer": "http://localhost:8000",
                        "X-Title": "CBC Lesson Plan Generator"
                    }

                    # Use explicit timeout for MoE processing
                    timeout = aiohttp.ClientTimeout(total=self.request_timeout)
                    async with self.session.post(self.base_url, json=payload, headers=headers, timeout=timeout) as response:
                        if response.status == 200:
                            result = await response.json()
                            ai_response = result.get('choices', [{}])[0].get('message', {}).get('content', '')

                            print(f"✅ SUCCESS: Model {model} responded with {len(ai_response)} characters")

                            # Parse and validate response
                            parsed_data = self._parse_ai_response(ai_response)
                            if parsed_data:
                                print(f"🎯 PARSED: {len(parsed_data)} lesson plans extracted")
                                # Cache successful response
                                self.response_cache[cache_key] = {
                                    "data": parsed_data,
                                    "timestamp": time.time()
                                }
                                return parsed_data
                            else:
                                print(f"⚠️ PARSE FAILED: Could not parse AI response from {model}")
                        else:
                            print(f"❌ API ERROR: Status {response.status} for model {model}")
                            error_text = await response.text()
                            print(f"Error details: {error_text[:200]}...")
                            self.logger.warning(f"AI API error {response.status} for model {model}")
                            
                except asyncio.TimeoutError:
                    print(f"⏰ TIMEOUT: Model {model} timed out after {self.request_timeout}s")
                    self.logger.warning(f"Timeout for model {model}")
                except Exception as e:
                    print(f"❌ ERROR: Model {model} failed: {str(e)}")
                    self.logger.error(f"AI request error for model {model}: {e}")
                    
        return None
        
    def _create_optimized_prompt(self, content: str) -> str:
        """Create optimized prompt for CBC curriculum processing"""
        return f"""Analyze this CBC (Competency-Based Curriculum) scheme of work document and extract detailed lesson plans. Return a JSON array with complete lesson plan data:

DOCUMENT CONTENT:
{content}

REQUIRED JSON FORMAT - Extract ALL weeks/lessons found:
[{{
  "week": 1,
  "lessonNumber": 1,
  "strand": "Main topic/theme",
  "sub_strand": "Specific subtopic",
  "specific_learning_outcomes": ["By the end of the lesson learner should be able to...", "Second outcome..."],
  "key_inquiry_question": "What/How/Why question from the document?",
  "learning_experiences": ["Activity 1 description", "Activity 2 description"],
  "learning_resources": ["Resource 1", "Resource 2"],
  "assessment": "Assessment method described",
  "pcis": ["PCI 1", "PCI 2"],
  "values": ["Value 1", "Value 2"],
  "core_competencies": ["Competency 1", "Competency 2"],
  "school": "CBC School",
  "level": "Grade X",
  "learning_area": "Subject name from document",
  "term": "Term X",
  "subject": "Subject name",
  "date": "2025-07-03",
  "time": "40 minutes"
}}]

INSTRUCTIONS:
- Extract ALL weeks/lessons found in the document
- Use exact text from the document where possible
- If information is missing, use appropriate defaults
- Identify the subject/learning area from the document content
- Return ONLY the JSON array, no other text
- Ensure valid JSON format"""
        
    def _parse_ai_response(self, response: str) -> Optional[List[Dict]]:
        """Parse and validate AI response"""
        try:
            # Clean response
            response = response.strip()
            if response.startswith('```json'):
                response = response[7:]
            if response.endswith('```'):
                response = response[:-3]
            response = response.strip()
            
            # Parse JSON
            data = json.loads(response)
            if isinstance(data, list) and len(data) > 0:
                # Validate structure
                for item in data:
                    if not isinstance(item, dict) or 'week' not in item:
                        return None
                return data
                
        except (json.JSONDecodeError, TypeError, KeyError):
            pass
            
        return None
        
    @monitor_performance("optimized_ai_processing")
    async def process_scheme_fast(self, content: str, filename: str = "") -> Dict:
        """
        Fast scheme processing with aggressive optimizations
        Target: Under 30 seconds for any document
        """
        request_id = f"fast_process_{int(time.time() * 1000)}"
        
        try:
            # Step 1: Quick preprocessing
            with track_component_time(request_id, "preprocessing"):
                processed_content = self._preprocess_content(content)
                if len(processed_content) < 50:
                    return {
                        "success": False,
                        "message": "Content too short to process",
                        "processing_time": 0
                    }
            
            # Step 2: Try fast models in parallel (limited concurrency)
            with track_component_time(request_id, "parallel_ai_processing"):
                tasks = []
                for model in self.fast_models[:2]:  # Only use 2 fastest models
                    task = self._make_ai_request(processed_content, model, request_id)
                    tasks.append(task)
                
                # Wait for first successful response
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # Find first successful result
                successful_result = None
                for result in results:
                    if isinstance(result, list) and len(result) > 0:
                        successful_result = result
                        break
            
            # Step 3: Generate lesson plans from AI result
            if successful_result:
                with track_component_time(request_id, "lesson_plan_generation"):
                    lesson_plans = self._generate_fast_lesson_plans(successful_result)
                    weeks_found = sorted(list(set([lp.get('week', 1) for lp in lesson_plans])))
                    
                    return {
                        "success": True,
                        "message": f"⚡ Fast AI Processing Complete - {len(lesson_plans)} lessons in {len(weeks_found)} weeks",
                        "lesson_plans": lesson_plans,
                        "weeks_found": weeks_found,
                        "total_weeks": len(weeks_found),
                        "confidence": 0.90,
                        "processing_method": "OPTIMIZED_FAST_AI",
                        "content_length": len(content),
                        "processed_length": len(processed_content)
                    }
            
            # Step 4: Fallback to pattern-based parsing if AI fails
            with track_component_time(request_id, "fallback_parsing"):
                return self._fallback_pattern_parsing(content)
                
        except Exception as e:
            self.logger.error(f"Fast processing error: {e}")
            return {
                "success": False,
                "message": f"Processing failed: {str(e)}",
                "error": str(e)
            }
            
    def _generate_fast_lesson_plans(self, ai_data: List[Dict]) -> List[Dict]:
        """Generate lesson plans quickly with minimal processing"""
        lesson_plans = []
        
        for i, lesson_data in enumerate(ai_data):
            lesson_plan = {
                # Basic info
                "school": "CBC School",
                "level": "Grade 9", 
                "learning_area": "Pre-Technical Studies",
                "date": f"2025-01-{6 + (lesson_data.get('week', i+1) * 7):02d}",
                "time": "40 minutes",
                "roll": "30 students",
                
                # Core lesson data
                "week": lesson_data.get('week', i+1),
                "lessonNumber": i+1,
                "title": f"Week {lesson_data.get('week', i+1)}: {lesson_data.get('sub_strand', 'Topic')}",
                "strand": lesson_data.get('strand', 'Pre-Technical Studies'),
                "sub_strand": lesson_data.get('sub_strand', 'General Topic'),
                
                # Learning components (use AI data or defaults)
                "specific_learning_outcomes": lesson_data.get('specific_learning_outcomes', [
                    "Students will demonstrate understanding of the topic"
                ]),
                "core_competencies": lesson_data.get('core_competencies', [
                    "Critical thinking", "Communication", "Creativity"
                ]),
                "pcis": lesson_data.get('pcis', ["Life skills", "Environmental awareness"]),
                "values": lesson_data.get('values', ["Responsibility", "Respect"]),
                "key_inquiry_question": lesson_data.get('key_inquiry_question', 
                    f"How can we apply {lesson_data.get('sub_strand', 'this topic')}?"),
                "learning_experiences": lesson_data.get('learning_experiences', [
                    "Interactive discussion", "Hands-on activities"
                ]),
                "learning_resources": lesson_data.get('learning_resources', [
                    "Textbooks", "Visual aids"
                ]),
                "assessment": lesson_data.get('assessment', "Observation and questioning"),
                "reflection": "Lesson effectiveness evaluation"
            }
            
            lesson_plans.append(lesson_plan)
            
        return lesson_plans
        
    def _fallback_pattern_parsing(self, content: str) -> Dict:
        """Fast pattern-based parsing as fallback"""
        # Simple regex-based extraction
        week_pattern = r'WEEK\s+(\d+)[:\s]*([^\n]+)'
        weeks = re.findall(week_pattern, content, re.IGNORECASE)
        
        if not weeks:
            # Try alternative patterns
            week_pattern = r'(\d+)[.\s]*([^\n]+(?:strand|topic|lesson))'
            weeks = re.findall(week_pattern, content, re.IGNORECASE)
        
        lesson_plans = []
        for i, (week_num, title) in enumerate(weeks[:10]):  # Limit to 10 weeks
            lesson_plans.append({
                "school": "CBC School",
                "level": "Grade 9",
                "learning_area": "Pre-Technical Studies", 
                "week": int(week_num) if week_num.isdigit() else i+1,
                "lessonNumber": i+1,
                "title": f"Week {week_num}: {title.strip()}",
                "strand": "Pre-Technical Studies",
                "sub_strand": title.strip()[:50],
                "specific_learning_outcomes": ["Students will demonstrate understanding"],
                "core_competencies": ["Critical thinking", "Communication"],
                "pcis": ["Life skills"],
                "values": ["Responsibility"],
                "key_inquiry_question": f"How can we understand {title.strip()[:30]}?",
                "learning_experiences": ["Discussion and practice"],
                "learning_resources": ["Textbooks and materials"],
                "assessment": "Observation",
                "reflection": "Evaluate lesson effectiveness"
            })
        
        weeks_found = [lp["week"] for lp in lesson_plans]
        
        return {
            "success": True,
            "message": f"📋 Pattern-based parsing complete - {len(lesson_plans)} lessons found",
            "lesson_plans": lesson_plans,
            "weeks_found": weeks_found,
            "total_weeks": len(weeks_found),
            "confidence": 0.75,
            "processing_method": "PATTERN_FALLBACK"
        }

# Global optimized processor instance
optimized_processor = OptimizedAIProcessor()
