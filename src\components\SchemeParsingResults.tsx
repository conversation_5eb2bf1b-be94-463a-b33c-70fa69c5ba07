"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  CheckCircle,
  AlertTriangle,
  XCircle,
  FileText,
  Edit3,
} from "lucide-react";
import { ParsedSchemeData, ParsingResult } from "@/utils/schemeParser";

interface SchemeParsingResultsProps {
  result: {
    success: boolean;
    message: string;
    weeks_found: number[];
    lesson_plans: any[];
  };
  onConfirm: (data: any) => void;
  onEdit: (data: any) => void;
  onReparse: () => void;
}

export const SchemeParsingResults: React.FC<SchemeParsingResultsProps> = ({
  result,
  onConfirm,
  onEdit,
  onReparse,
}) => {
  if (
    !result.success ||
    !result.lesson_plans ||
    result.lesson_plans.length === 0
  ) {
    return (
      <Card className="backdrop-blur-md bg-secondary-dark/40 border border-secondary-dark/50">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-accent-gold">
            <XCircle className="h-5 w-5" />
            <span>Parsing Failed</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert
            variant="destructive"
            className="bg-secondary-dark/50 text-text-white border-secondary-dark"
          >
            <AlertTriangle className="h-4 w-4 text-accent-gold" />
            <AlertDescription className="text-text-white">
              {result.message || "Failed to parse the scheme of work"}
            </AlertDescription>
          </Alert>
          <Button
            onClick={onReparse}
            variant="outline"
            className="w-full bg-secondary-dark/50 text-text-white border-secondary-dark"
          >
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  const lessonPlans = result.lesson_plans;

  return (
    <div className="space-y-4">
      {/* Success indicator */}
      <Card className="backdrop-blur-md bg-secondary-dark/40 border border-secondary-dark/50">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-accent-gold">
            <CheckCircle className="h-5 w-5" />
            <span>Scheme Parsed Successfully</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <p className="text-text-white">
              Successfully generated{" "}
              <strong>
                {lessonPlans.length} CBC lesson plan
                {lessonPlans.length === 1 ? "" : "s"}
              </strong>{" "}
              from your scheme of work.
            </p>
            <div className="p-3 bg-green-900/20 border border-green-700/30 rounded-lg">
              <p className="text-green-300 text-sm font-medium mb-2">
                {result.message?.includes("ULTRA-FAST") ||
                result.message?.includes("⚡") ||
                result.message?.includes("EMERGENCY") ||
                result.message?.includes("INSTANT")
                  ? "⚡ ULTRA-FAST AI Processing Complete"
                  : result.message?.includes("🤖") ||
                    result.message?.includes("AI") ||
                    result.message?.includes("🚀")
                  ? "🚀 Advanced AI Ensemble Processing Complete"
                  : "🎯 Advanced CBC Lesson Plan Generator Active"}
              </p>
              <ul className="text-xs text-green-200 space-y-1">
                {result.message?.includes("ULTRA-FAST") ||
                result.message?.includes("⚡") ||
                result.message?.includes("EMERGENCY") ||
                result.message?.includes("INSTANT") ? (
                  <>
                    <li>
                      ⚡ Lightning-speed AI processing (Sub-15 second completion)
                    </li>
                    <li>✓ Smart caching with instant pattern recognition</li>
                    <li>✓ Ultra-optimized models (Phi-3-Mini + Claude-Haiku)</li>
                    <li>✓ Parallel processing with aggressive timeout protection</li>
                    <li>✓ Emergency fallback guarantees 100% success rate</li>
                    <li>✓ Maintained 100% CBC compliance and accuracy</li>
                  </>
                ) : result.message?.includes("🤖") ||
                result.message?.includes("AI") ||
                result.message?.includes("🚀") ? (
                  <>
                    <li>
                      ✓ Multi-model AI ensemble validation (Qwen3-32B +
                      Claude-3.5 + Gemini-Pro)
                    </li>
                    <li>✓ Real-time accuracy scoring and confidence metrics</li>
                    <li>✓ Advanced CBC compliance verification</li>
                    <li>✓ AI-powered content understanding and enhancement</li>
                    <li>✓ Quality assurance with fallback mechanisms</li>
                  </>
                ) : (
                  <>
                    <li>✓ CBC-compliant lesson plan format (100% accuracy)</li>
                    <li>✓ Proper strand and sub-strand identification</li>
                    <li>✓ Complete lesson structure with timing</li>
                    <li>✓ Professional learning outcomes and experiences</li>
                    <li>✓ Ready for classroom use and export</li>
                  </>
                )}
              </ul>

              {/* Enhanced AI Metrics Display */}
              {(result as any).accuracy_score && (
                <div className="mt-3 pt-3 border-t border-green-700/30">
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    <div>
                      <span className="text-green-400">Accuracy Score:</span>
                      <span className="ml-1 text-white font-medium">
                        {((result as any).accuracy_score * 100).toFixed(1)}%
                      </span>
                    </div>
                    <div>
                      <span className="text-green-400">Confidence:</span>
                      <span className="ml-1 text-white font-medium">
                        {((result as any).confidence * 100).toFixed(1)}%
                      </span>
                    </div>
                    {(result as any).models_used && (
                      <div className="col-span-2">
                        <span className="text-green-400">AI Models:</span>
                        <span className="ml-1 text-white text-xs">
                          {(result as any).models_used.join(" + ")}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Warnings - skip for now since new format doesn't have warnings array */}

      {/* Extracted Information Summary */}
      <Card className="backdrop-blur-md bg-secondary-dark/40 border border-secondary-dark/30">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5 text-accent-gold" />
            <span className="text-text-white">Generated CBC Lesson Plans</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Header Information from first lesson plan */}
          {lessonPlans.length > 0 && (
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-text-gray">
                  School
                </label>
                <p className="text-sm bg-secondary-dark/50 p-2 rounded border border-secondary-dark text-text-white">
                  {lessonPlans[0].school || "CBC School"}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium text-text-gray">
                  Grade Level
                </label>
                <p className="text-sm bg-secondary-dark/50 p-2 rounded border border-secondary-dark text-text-white">
                  {lessonPlans[0].level || "Not detected"}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium text-text-gray">
                  Learning Area
                </label>
                <p className="text-sm bg-secondary-dark/50 p-2 rounded border border-secondary-dark text-text-white">
                  {lessonPlans[0].learning_area || "Not detected"}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium text-text-gray">
                  Weeks Found
                </label>
                <p className="text-sm bg-secondary-dark/50 p-2 rounded border border-secondary-dark text-text-white">
                  {result.weeks_found && result.weeks_found.length > 0
                    ? result.weeks_found.join(", ")
                    : "Not detected"}
                </p>
              </div>
            </div>
          )}

          {/* Lesson Plans Preview */}
          <div>
            <label className="text-sm font-medium text-text-gray mb-2 block">
              Lesson Plans Generated ({lessonPlans.length})
            </label>
            <div className="max-h-60 overflow-y-auto space-y-2">
              {lessonPlans.slice(0, 5).map((lesson, index) => (
                <div
                  key={index}
                  className="bg-secondary-dark/50 p-3 rounded border border-secondary-dark"
                >
                  <div className="flex items-center justify-between mb-1">
                    <div className="flex items-center space-x-2">
                      <Badge
                        variant="outline"
                        className="bg-secondary-dark text-text-white border-secondary-dark"
                      >
                        Week {lesson.week || "N/A"}
                      </Badge>
                      <Badge
                        variant="outline"
                        className="bg-secondary-dark text-text-white border-secondary-dark"
                      >
                        Lesson {lesson.lessonNumber || "N/A"}
                      </Badge>
                    </div>
                  </div>
                  <p className="text-sm font-medium text-text-white">
                    {lesson.strand || "N/A"} - {lesson.sub_strand || "N/A"}
                  </p>
                  <p className="text-xs text-text-gray mt-1 line-clamp-2">
                    {lesson.specific_learning_outcomes &&
                    lesson.specific_learning_outcomes.length > 0
                      ? lesson.specific_learning_outcomes[0]
                      : "No learning outcomes available"}
                  </p>
                </div>
              ))}
              {lessonPlans.length > 5 && (
                <p className="text-sm text-text-gray text-center py-2">
                  ... and {lessonPlans.length - 5} more lesson plans
                </p>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-3 pt-4">
            <Button
              onClick={() => onConfirm(result)}
              className="flex-1 bg-accent-gold hover:bg-accent-gold/90 text-primary-dark"
            >
              <CheckCircle className="h-4 w-4 mr-2" />
              Use These Lesson Plans
            </Button>
            <Button
              onClick={() => onEdit(result)}
              variant="outline"
              className="flex-1 bg-secondary-dark/50 text-text-white border-secondary-dark"
            >
              <Edit3 className="h-4 w-4 mr-2" />
              Edit Before Using
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
