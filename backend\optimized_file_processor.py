"""
Optimized File Processing System
Fast and efficient document text extraction
"""
import fitz  # PyMuPDF - fastest PDF processor
import docx
import io
import time
import logging
from typing import Dict, Optional, Tuple
from pathlib import Path
from performance_monitor import performance_monitor, track_component_time

class OptimizedFileProcessor:
    """
    High-performance file processor optimized for speed
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.max_file_size = 50 * 1024 * 1024  # 50MB limit
        self.max_pages = 100  # Limit pages for performance
        self.max_text_length = 500000  # 500k characters max
        
    def extract_text_fast(self, file_content: bytes, filename: str, request_id: str) -> Tuple[str, Dict]:
        """
        Extract text from file with optimized performance
        """
        file_ext = Path(filename).suffix.lower()
        
        # Check file size
        if len(file_content) > self.max_file_size:
            return "", {
                "success": False,
                "error": f"File too large: {len(file_content)} bytes (max: {self.max_file_size})",
                "file_size": len(file_content)
            }
        
        try:
            if file_ext == '.pdf':
                return self._extract_pdf_fast(file_content, request_id)
            elif file_ext in ['.docx', '.doc']:
                return self._extract_docx_fast(file_content, request_id)
            elif file_ext == '.txt':
                return self._extract_txt_fast(file_content, request_id)
            else:
                return "", {
                    "success": False,
                    "error": f"Unsupported file type: {file_ext}",
                    "supported_types": [".pdf", ".docx", ".doc", ".txt"]
                }
                
        except Exception as e:
            self.logger.error(f"File extraction error: {e}")
            return "", {
                "success": False,
                "error": f"Extraction failed: {str(e)}"
            }
    
    def _extract_pdf_fast(self, file_content: bytes, request_id: str) -> Tuple[str, Dict]:
        """Fast PDF text extraction using PyMuPDF"""
        with track_component_time(request_id, "pdf_extraction"):
            doc = None
            try:
                # Use PyMuPDF for fastest extraction
                doc = fitz.open(stream=file_content, filetype="pdf")

                text_parts = []
                pages_processed = 0
                total_pages = len(doc)

                for page_num in range(min(total_pages, self.max_pages)):
                    try:
                        page = doc[page_num]
                        page_text = page.get_text("text")

                        if page_text.strip():
                            text_parts.append(page_text)
                            pages_processed += 1

                        # Check if we have enough text
                        current_length = sum(len(part) for part in text_parts)
                        if current_length > self.max_text_length:
                            break

                    except Exception as page_error:
                        self.logger.warning(f"Error processing page {page_num}: {page_error}")
                        continue

                full_text = "\n".join(text_parts)

                # Truncate if still too long
                if len(full_text) > self.max_text_length:
                    full_text = full_text[:self.max_text_length]

                return full_text, {
                    "success": True,
                    "pages_processed": pages_processed,
                    "total_pages": total_pages,
                    "text_length": len(full_text),
                    "extraction_method": "PyMuPDF_fast"
                }

            except Exception as e:
                self.logger.error(f"PDF extraction error: {e}")
                return "", {
                    "success": False,
                    "error": f"PDF extraction failed: {str(e)}"
                }
            finally:
                # Ensure document is properly closed
                if doc is not None:
                    try:
                        doc.close()
                    except:
                        pass
    
    def _extract_docx_fast(self, file_content: bytes, request_id: str) -> Tuple[str, Dict]:
        """Fast DOCX text extraction"""
        with track_component_time(request_id, "docx_extraction"):
            try:
                doc = docx.Document(io.BytesIO(file_content))
                
                text_parts = []
                paragraphs_processed = 0
                
                for paragraph in doc.paragraphs:
                    if paragraph.text.strip():
                        text_parts.append(paragraph.text)
                        paragraphs_processed += 1
                    
                    # Check length limit
                    current_length = sum(len(part) for part in text_parts)
                    if current_length > self.max_text_length:
                        break
                
                # Also extract from tables
                for table in doc.tables:
                    for row in table.rows:
                        for cell in row.cells:
                            if cell.text.strip():
                                text_parts.append(cell.text)
                    
                    # Check length limit
                    current_length = sum(len(part) for part in text_parts)
                    if current_length > self.max_text_length:
                        break
                
                full_text = "\n".join(text_parts)
                
                # Truncate if needed
                if len(full_text) > self.max_text_length:
                    full_text = full_text[:self.max_text_length]
                
                return full_text, {
                    "success": True,
                    "paragraphs_processed": paragraphs_processed,
                    "text_length": len(full_text),
                    "extraction_method": "python-docx_fast"
                }
                
            except Exception as e:
                self.logger.error(f"DOCX extraction error: {e}")
                return "", {
                    "success": False,
                    "error": f"DOCX extraction failed: {str(e)}"
                }
    
    def _extract_txt_fast(self, file_content: bytes, request_id: str) -> Tuple[str, Dict]:
        """Fast text file processing"""
        with track_component_time(request_id, "txt_extraction"):
            try:
                # Try different encodings
                encodings = ['utf-8', 'utf-16', 'latin-1', 'cp1252']
                
                for encoding in encodings:
                    try:
                        text = file_content.decode(encoding)
                        
                        # Truncate if needed
                        if len(text) > self.max_text_length:
                            text = text[:self.max_text_length]
                        
                        return text, {
                            "success": True,
                            "text_length": len(text),
                            "encoding_used": encoding,
                            "extraction_method": "direct_text"
                        }
                        
                    except UnicodeDecodeError:
                        continue
                
                # If all encodings fail
                return "", {
                    "success": False,
                    "error": "Could not decode text file with any supported encoding",
                    "encodings_tried": encodings
                }
                
            except Exception as e:
                self.logger.error(f"Text extraction error: {e}")
                return "", {
                    "success": False,
                    "error": f"Text extraction failed: {str(e)}"
                }
    
    def preprocess_text_for_ai(self, text: str, request_id: str) -> str:
        """Optimize text for AI processing"""
        with track_component_time(request_id, "text_preprocessing"):
            if not text:
                return ""
            
            # Remove excessive whitespace
            import re
            text = re.sub(r'\s+', ' ', text.strip())
            
            # Remove very long lines (likely formatting artifacts)
            lines = text.split('\n')
            cleaned_lines = []
            for line in lines:
                if len(line) < 1000:  # Skip very long lines
                    cleaned_lines.append(line)
            
            text = '\n'.join(cleaned_lines)
            
            # Ensure reasonable length for AI processing
            if len(text) > 20000:  # 20k chars for AI
                # Try to find good breaking points
                sentences = text.split('.')
                truncated = ""
                for sentence in sentences:
                    if len(truncated + sentence) > 20000:
                        break
                    truncated += sentence + "."
                text = truncated if truncated else text[:20000]
            
            return text
    
    def get_file_info(self, file_content: bytes, filename: str) -> Dict:
        """Get file information quickly"""
        file_ext = Path(filename).suffix.lower()
        
        info = {
            "filename": filename,
            "file_extension": file_ext,
            "file_size": len(file_content),
            "file_size_mb": round(len(file_content) / (1024 * 1024), 2),
            "supported": file_ext in ['.pdf', '.docx', '.doc', '.txt'],
            "estimated_processing_time": self._estimate_processing_time(len(file_content), file_ext)
        }
        
        return info
    
    def _estimate_processing_time(self, file_size: int, file_ext: str) -> str:
        """Estimate processing time based on file size and type"""
        size_mb = file_size / (1024 * 1024)
        
        if file_ext == '.pdf':
            # PDF processing: ~1 second per MB
            estimated_seconds = max(1, int(size_mb * 1))
        elif file_ext in ['.docx', '.doc']:
            # DOCX processing: ~0.5 seconds per MB
            estimated_seconds = max(1, int(size_mb * 0.5))
        elif file_ext == '.txt':
            # Text processing: very fast
            estimated_seconds = 1
        else:
            estimated_seconds = 5
        
        if estimated_seconds < 60:
            return f"{estimated_seconds} seconds"
        else:
            minutes = estimated_seconds // 60
            seconds = estimated_seconds % 60
            return f"{minutes}m {seconds}s"

# Global file processor instance
optimized_file_processor = OptimizedFileProcessor()
