#!/usr/bin/env python3
"""
Teacher Customization System for CBC Lesson Plans
Allows teachers to personalize lesson plans based on their specific needs
"""

from typing import Dict, List, Optional
from dataclasses import dataclass, asdict
import json

@dataclass
class TeacherProfile:
    """Complete teacher profile for lesson plan customization"""
    
    # Basic Information
    teacher_name: str = "CBC Teacher"
    school_name: str = "CBC Primary School"
    school_type: str = "Public"  # Public, Private, International
    county: str = "Nairobi"
    
    # Class Information
    grade_level: str = "Grade 5"
    subject: str = "Science Technology"
    term: str = "Term 2"
    year: int = 2025
    class_size: int = 35
    
    # Teaching Preferences
    teaching_style: str = "Interactive"  # Interactive, Traditional, Inquiry-based, Practical
    language_of_instruction: str = "English"  # English, Kiswahili, Mixed
    assessment_preference: str = "Continuous"  # Continuous, Formal, Mixed
    
    # Resource Availability
    has_projector: bool = False
    has_computers: bool = False
    has_internet: bool = False
    has_science_lab: bool = False
    has_library: bool = True
    outdoor_space: bool = True
    
    # Customization Preferences
    include_local_examples: bool = True
    include_career_connections: bool = True
    include_cross_curricular: bool = True
    difficulty_level: str = "Standard"  # Basic, Standard, Advanced
    
    # Special Needs
    has_special_needs_students: bool = False
    special_needs_accommodations: List[str] = None
    
    # Time Preferences
    lesson_duration: int = 40  # minutes
    preferred_activities: List[str] = None  # ["Group work", "Experiments", "Discussions"]
    
    def __post_init__(self):
        if self.special_needs_accommodations is None:
            self.special_needs_accommodations = []
        if self.preferred_activities is None:
            self.preferred_activities = ["Group discussions", "Practical activities", "Individual work"]

class TeacherCustomizer:
    """Handles teacher-specific customization of lesson plans"""
    
    def __init__(self):
        self.customization_templates = {
            "Interactive": {
                "activities": ["Group discussions", "Role playing", "Interactive demonstrations", "Peer teaching"],
                "assessment": ["Oral questions", "Group presentations", "Peer assessment", "Quick polls"],
                "resources": ["Charts", "Models", "Interactive materials", "Group work sheets"]
            },
            "Traditional": {
                "activities": ["Teacher explanation", "Note taking", "Individual exercises", "Homework assignments"],
                "assessment": ["Written tests", "Individual assignments", "Oral recitation", "Homework review"],
                "resources": ["Textbooks", "Chalkboard", "Exercise books", "Reference materials"]
            },
            "Inquiry-based": {
                "activities": ["Research projects", "Investigations", "Problem solving", "Discovery learning"],
                "assessment": ["Project presentations", "Research reports", "Problem-solving tasks", "Reflection journals"],
                "resources": ["Research materials", "Investigation tools", "Problem scenarios", "Reflection templates"]
            },
            "Practical": {
                "activities": ["Hands-on experiments", "Field work", "Model making", "Practical demonstrations"],
                "assessment": ["Practical tests", "Project work", "Skill demonstrations", "Portfolio assessment"],
                "resources": ["Laboratory equipment", "Field guides", "Construction materials", "Safety equipment"]
            }
        }
        
        self.county_examples = {
            "Nairobi": ["Nairobi National Park", "Uhuru Park", "City markets", "Matatu transport"],
            "Mombasa": ["Indian Ocean", "Fort Jesus", "Dhow boats", "Coconut palms"],
            "Kisumu": ["Lake Victoria", "Fish markets", "Water hyacinth", "Fishing boats"],
            "Nakuru": ["Lake Nakuru", "Flamingos", "Rift Valley", "Geothermal energy"],
            "Eldoret": ["Athletics training", "Maize farming", "Highland climate", "Running tracks"],
            "Meru": ["Mount Kenya", "Coffee farming", "Forest conservation", "Mountain climbing"],
            "Kakamega": ["Kakamega Forest", "Indigenous trees", "Traditional medicine", "Forest conservation"]
        }
    
    def customize_lesson_plan(self, lesson_plan: Dict, teacher_profile: TeacherProfile) -> Dict:
        """Customize a lesson plan based on teacher profile"""
        
        # Create customized copy
        customized_lesson = lesson_plan.copy()
        
        # Basic customization
        customized_lesson.update({
            "school": teacher_profile.school_name,
            "teacher": teacher_profile.teacher_name,
            "level": teacher_profile.grade_level,
            "subject": teacher_profile.subject,
            "term": teacher_profile.term,
            "year": teacher_profile.year,
            "class_size": teacher_profile.class_size,
            "duration": f"{teacher_profile.lesson_duration} minutes",
            "language": teacher_profile.language_of_instruction
        })
        
        # Customize learning experiences based on teaching style
        if teacher_profile.teaching_style in self.customization_templates:
            template = self.customization_templates[teacher_profile.teaching_style]
            
            # Enhance learning experiences
            original_experiences = customized_lesson.get("learning_experiences", [])
            enhanced_experiences = []
            
            for experience in original_experiences:
                enhanced_experiences.append(experience)
                # Add style-specific activities
                if len(enhanced_experiences) < 4:  # Limit to 4 activities
                    enhanced_experiences.extend(template["activities"][:2])
            
            customized_lesson["learning_experiences"] = enhanced_experiences[:4]
            
            # Customize assessment methods
            customized_lesson["assessment"] = template["assessment"][0]  # Primary assessment
            customized_lesson["additional_assessments"] = template["assessment"][1:3]  # Additional options
            
            # Enhance resources based on availability
            enhanced_resources = list(customized_lesson.get("learning_resources", []))
            
            # Add technology resources if available
            if teacher_profile.has_projector:
                enhanced_resources.append("Projector and slides")
            if teacher_profile.has_computers:
                enhanced_resources.append("Computer/tablet activities")
            if teacher_profile.has_internet:
                enhanced_resources.append("Online resources and videos")
            if teacher_profile.has_science_lab:
                enhanced_resources.append("Laboratory equipment")
            
            customized_lesson["learning_resources"] = enhanced_resources
        
        # Add local examples
        if teacher_profile.include_local_examples and teacher_profile.county in self.county_examples:
            local_examples = self.county_examples[teacher_profile.county]
            customized_lesson["local_examples"] = local_examples[:3]
            customized_lesson["contextual_note"] = f"Use examples from {teacher_profile.county} to make learning relevant"
        
        # Add career connections
        if teacher_profile.include_career_connections:
            strand = customized_lesson.get("strand", "").lower()
            career_connections = self._get_career_connections(strand)
            customized_lesson["career_connections"] = career_connections
        
        # Adjust difficulty level
        if teacher_profile.difficulty_level != "Standard":
            customized_lesson["difficulty_adjustment"] = self._adjust_difficulty(
                customized_lesson, teacher_profile.difficulty_level
            )
        
        # Add special needs accommodations
        if teacher_profile.has_special_needs_students:
            customized_lesson["special_needs_accommodations"] = teacher_profile.special_needs_accommodations
            customized_lesson["inclusive_strategies"] = [
                "Use visual aids and demonstrations",
                "Provide clear, simple instructions",
                "Allow extra time for activities",
                "Use peer support systems"
            ]
        
        # Add cross-curricular connections
        if teacher_profile.include_cross_curricular:
            customized_lesson["cross_curricular"] = self._get_cross_curricular_connections(
                customized_lesson.get("strand", "")
            )
        
        return customized_lesson
    
    def _get_career_connections(self, strand: str) -> List[str]:
        """Get career connections based on lesson strand"""
        career_map = {
            "living things": ["Biologist", "Veterinarian", "Farmer", "Conservationist"],
            "materials": ["Engineer", "Architect", "Chemist", "Construction worker"],
            "energy": ["Electrical engineer", "Solar technician", "Physicist", "Energy consultant"],
            "forces": ["Mechanical engineer", "Pilot", "Sports scientist", "Safety inspector"],
            "earth": ["Geologist", "Meteorologist", "Environmental scientist", "Mining engineer"]
        }
        
        for key, careers in career_map.items():
            if key in strand:
                return careers[:3]
        
        return ["Scientist", "Teacher", "Researcher"]
    
    def _adjust_difficulty(self, lesson_plan: Dict, level: str) -> Dict:
        """Adjust lesson difficulty based on level"""
        adjustments = {}
        
        if level == "Basic":
            adjustments = {
                "focus": "Simplified concepts with more concrete examples",
                "activities": "More guided practice and repetition",
                "assessment": "Basic recall and simple application questions"
            }
        elif level == "Advanced":
            adjustments = {
                "focus": "Extended concepts with abstract thinking",
                "activities": "Independent research and complex problem solving",
                "assessment": "Analysis, synthesis, and evaluation questions"
            }
        
        return adjustments
    
    def _get_cross_curricular_connections(self, strand: str) -> Dict[str, List[str]]:
        """Get cross-curricular connections"""
        connections = {
            "Mathematics": ["Measurements", "Data collection", "Graphs and charts"],
            "English": ["Scientific vocabulary", "Report writing", "Reading comprehension"],
            "Social Studies": ["Environmental conservation", "Community health", "Technology in society"],
            "Creative Arts": ["Scientific drawings", "Model making", "Drama and role play"]
        }
        
        return connections
    
    def create_teacher_profile_from_dict(self, profile_dict: Dict) -> TeacherProfile:
        """Create TeacherProfile from dictionary"""
        return TeacherProfile(**profile_dict)
    
    def save_teacher_profile(self, profile: TeacherProfile, filename: str):
        """Save teacher profile to JSON file"""
        with open(filename, 'w') as f:
            json.dump(asdict(profile), f, indent=2)
    
    def load_teacher_profile(self, filename: str) -> TeacherProfile:
        """Load teacher profile from JSON file"""
        with open(filename, 'r') as f:
            profile_dict = json.load(f)
        return TeacherProfile(**profile_dict)

# Example usage and presets
TEACHER_PRESETS = {
    "urban_public": TeacherProfile(
        school_type="Public",
        class_size=45,
        has_projector=False,
        has_computers=False,
        has_internet=False,
        teaching_style="Interactive"
    ),
    
    "rural_primary": TeacherProfile(
        school_type="Public",
        class_size=30,
        has_projector=False,
        has_computers=False,
        has_internet=False,
        outdoor_space=True,
        teaching_style="Practical",
        include_local_examples=True
    ),
    
    "private_school": TeacherProfile(
        school_type="Private",
        class_size=25,
        has_projector=True,
        has_computers=True,
        has_internet=True,
        has_science_lab=True,
        teaching_style="Inquiry-based",
        difficulty_level="Advanced"
    ),
    
    "special_needs": TeacherProfile(
        class_size=15,
        has_special_needs_students=True,
        special_needs_accommodations=[
            "Visual learning aids",
            "Simplified instructions",
            "Extra time allowance",
            "Peer support"
        ],
        teaching_style="Interactive",
        difficulty_level="Basic"
    )
}
