"use client";

import React, { useState, useRef } from "react";
import dynamic from "next/dynamic";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Upload, FileText, ClipboardPaste, Zap, CheckCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { SchemeParsingResults } from "@/components/SchemeParsingResults";
import { SchemeUploadGuidance } from "@/components/SchemeUploadGuidance";
import { ParsingResult } from "@/utils/schemeParser";

// API Configuration
const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8000";

// Dynamically import File icon to disable SSR
const FileIcon = dynamic(() => import("lucide-react").then((mod) => mod.File), {
  ssr: false,
});

interface FileUploadProps {
  onUpload: (content: string) => void;
  onParsedDataReady?: (result: ParsingResult) => void;
}

export const FileUpload: React.FC<FileUploadProps> = ({
  onUpload,
  onParsedDataReady,
}) => {
  const [textContent, setTextContent] = useState("");
  const [dragOver, setDragOver] = useState(false);
  const [uploadedFileName, setUploadedFileName] = useState<string | null>(null);
  const [parsingResult, setParsingResult] = useState<ParsingResult | null>(
    null
  );
  const [isProcessing, setIsProcessing] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const acceptedFormats = [
    ".pdf",
    ".doc",
    ".docx",
    ".xls",
    ".xlsx",
    ".txt",
    ".rtf",
    ".odt",
    ".ods",
  ];

  const getFileIcon = (fileName: string | null) => {
    return <FileIcon className="h-4 w-4" />;
  };

  const parseFile = async (file: File) => {
    setIsProcessing(true);
    setParsingResult(null);

    const formData = new FormData();
    formData.append("file", file);

    try {
      console.log(`Making request to: ${API_BASE_URL}/parse-scheme/`);

      const response = await fetch(`${API_BASE_URL}/parse-scheme/`, {
        method: "POST",
        body: formData,
      });

      console.log("Response status:", response.status);
      console.log("Response headers:", response.headers);

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Error response:", errorText);

        // Try to parse as JSON, fallback to text
        let errorMessage;
        try {
          const errorData = JSON.parse(errorText);
          errorMessage = errorData.detail || "Failed to parse file on backend.";
        } catch {
          errorMessage = `Server error: ${response.status} - ${errorText}`;
        }
        throw new Error(errorMessage);
      }

      const data = await response.json();
      console.log("Parsed data:", data);

      const result: ParsingResult = {
        success: data.success,
        data: data.success
          ? {
              title: "Parsed Scheme of Work",
              weeks: data.lesson_plans.map((lp: any) => ({
                week: lp.week,
                lesson: lp.lessonNumber || 1,
                strand: lp.strand,
                subStrand: lp.sub_strand,
                lessonLearningOutcome: lp.specific_learning_outcomes.join("\n"),
                learningExperiences: lp.activities.join("\n"),
                keyInquiryQuestion: lp.key_inquiry_question,
                learningResources: lp.learning_resources.join("\n"),
                assessment: lp.assessment,
                reflection: lp.reflection,
              })),
              term: data.lesson_plans[0]?.term?.toString() || undefined,
            }
          : null,
        errors: data.success ? [] : [data.message],
        warnings: [],
      };

      setParsingResult(result);
      if (onParsedDataReady) {
        onParsedDataReady(result);
      }

      if (result.success) {
        toast({
          title: "Scheme parsed successfully!",
          description: `Found ${result.data?.weeks.length} lessons. Please review the extracted information.`,
        });
      } else {
        // For parsing failures, show helpful guidance
        const errorMessage =
          result.errors[0] || "Could not parse the scheme format.";
        let guidance = "";

        if (
          errorMessage.includes("structure") ||
          errorMessage.includes("format")
        ) {
          guidance =
            " Try uploading a different format or check if your scheme follows CBC standards.";
        } else if (
          errorMessage.includes("week") ||
          errorMessage.includes("lesson")
        ) {
          guidance =
            " Make sure your scheme has clear week and lesson indicators.";
        } else if (
          errorMessage.includes("server") ||
          errorMessage.includes("connect")
        ) {
          guidance = " Please check your internet connection and try again.";
        }

        toast({
          title: "Parsing encountered issues",
          description: errorMessage + guidance,
          variant: "destructive",
        });

        // Still allow user to proceed to manual configuration
        if (onParsedDataReady) {
          const fallbackResult: ParsingResult = {
            success: false,
            data: {
              title: "Manual Configuration Required",
              weeks: [],
              term: undefined,
            },
            errors: [errorMessage],
            warnings: [
              "You can still proceed with manual lesson plan configuration.",
            ],
          };
          onParsedDataReady(fallbackResult);
        }
      }
    } catch (error: any) {
      console.error("File upload and parsing error:", error);

      let errorMessage = "An unexpected error occurred during file processing.";

      if (error.message.includes("fetch")) {
        errorMessage = `Cannot connect to backend server at ${API_BASE_URL}. Make sure the backend is running.`;
      } else {
        errorMessage = error.message;
      }

      toast({
        title: "Error processing file",
        description: errorMessage,
        variant: "destructive",
      });

      setParsingResult({
        success: false,
        data: null,
        errors: [errorMessage],
        warnings: [],
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setUploadedFileName(file.name);
      parseFile(file);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);

    if (e.dataTransfer.files?.length) {
      const file = e.dataTransfer.files[0];
      setUploadedFileName(file.name);
      parseFile(file);
    }
  };

  const handleTextSubmit = async () => {
    if (!textContent.trim()) {
      toast({
        title: "Text content is empty",
        description: "Please enter your scheme of work content.",
        variant: "destructive",
      });
      return;
    }

    setIsProcessing(true);
    setParsingResult(null);

    try {
      const response = await fetch(`${API_BASE_URL}/parse-text-scheme/`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ content: textContent }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Error response:", errorText);

        let errorMessage;
        try {
          const errorData = JSON.parse(errorText);
          errorMessage = errorData.detail || "Failed to parse text content.";
        } catch {
          errorMessage = `Server error: ${response.status} - ${errorText}`;
        }
        throw new Error(errorMessage);
      }

      const data = await response.json();
      console.log("Parsed data:", data);

      onUpload(textContent);

      const result: ParsingResult = {
        success: data.success,
        data: data.success
          ? {
              title: "Parsed Scheme of Work (Text Input)",
              weeks: data.lesson_plans.map((lp: any) => ({
                week: lp.week,
                lesson: lp.lessonNumber || 1,
                strand: lp.strand,
                subStrand: lp.sub_strand,
                lessonLearningOutcome: lp.specific_learning_outcomes.join("\n"),
                learningExperiences: lp.activities.join("\n"),
                keyInquiryQuestion: lp.key_inquiry_question,
                learningResources: lp.learning_resources.join("\n"),
                assessment: lp.assessment,
                reflection: lp.reflection,
              })),
              term: data.lesson_plans[0]?.term?.toString() || undefined,
            }
          : null,
        errors: data.success ? [] : [data.message],
        warnings: [],
      };

      setParsingResult(result);
      if (onParsedDataReady) {
        onParsedDataReady(result);
      }

      if (result.success) {
        toast({
          title: "Scheme parsed successfully!",
          description: `Found ${result.data?.weeks.length} lessons from text.`,
        });
      } else {
        const errorMessage = result.errors[0] || "Could not parse the text content.";
        let guidance = "";

        if (errorMessage.includes("structure") || errorMessage.includes("format")) {
          guidance = " Try formatting your text with clear sections for each week/lesson.";
        } else if (errorMessage.includes("week") || errorMessage.includes("lesson")) {
          guidance = " Make sure your text includes week numbers and lesson details.";
        }

        toast({
          title: "Parsing encountered issues",
          description: errorMessage + guidance,
          variant: "destructive",
        });

        if (onParsedDataReady) {
          const fallbackResult: ParsingResult = {
            success: false,
            data: {
              title: "Manual Configuration Required",
              weeks: [],
              term: undefined,
            },
            errors: [errorMessage],
            warnings: ["You can still proceed with manual lesson plan configuration."],
          };
          onParsedDataReady(fallbackResult);
        }
      }
    } catch (error: any) {
      console.error("Text parsing error:", error);

      let errorMessage = "An unexpected error occurred during text processing.";

      if (error.message.includes("fetch")) {
        errorMessage = `Cannot connect to backend server at ${API_BASE_URL}. Make sure the backend is running.`;
      } else {
        errorMessage = error.message;
      }

      toast({
        title: "Error processing text",
        description: errorMessage,
        variant: "destructive",
      });

      setParsingResult({
        success: false,
        data: null,
        errors: [errorMessage],
        warnings: [],
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div>
      {parsingResult ? (
        <SchemeParsingResults
          result={parsingResult}
          onConfirm={(data) => {
            if (onParsedDataReady) {
              onParsedDataReady({ ...parsingResult, data });
            }
          }}
          onEdit={(data) => {
            if (onParsedDataReady) {
              onParsedDataReady({ ...parsingResult, data });
            }
          }}
          onReparse={() => {
            setParsingResult(null);
            setUploadedFileName(null);
            setTextContent("");
          }}
        />
      ) : (
        <div className="space-y-8">
          <SchemeUploadGuidance />

          {isProcessing && (
            <Card className="mt-8 backdrop-blur-xl bg-white/70 dark:bg-slate-800/70 border border-blue-200/50 dark:border-blue-800/30 shadow-xl rounded-xl overflow-hidden">
              <div className="absolute top-0 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-500 via-purple-500 to-indigo-500">
                <div className="h-full w-40 bg-gradient-to-r from-blue-300/0 via-white/80 to-blue-300/0 animate-shimmer"></div>
              </div>
              <CardContent className="p-6">
                <div className="flex items-center gap-5">
                  <div className="relative flex-shrink-0">
                    <div className="absolute inset-0 rounded-full bg-gradient-to-r from-blue-400 to-indigo-500 opacity-20 blur-md animate-pulse"></div>
                    <div className="relative bg-gradient-to-r from-blue-600 to-indigo-600 p-3 rounded-full">
                      <Zap className="h-6 w-6 text-white animate-bounce-gentle" />
                    </div>
                  </div>
                  
                  <div className="space-y-1">
                    <h3 className="text-lg font-semibold text-slate-800 dark:text-slate-200">
                      Processing Your Scheme of Work
                    </h3>
                    <div className="space-y-2">
                      <p className="text-slate-600 dark:text-slate-400">
                        Our AI is analyzing your content to extract:
                      </p>
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <div className="w-1.5 h-1.5 rounded-full bg-blue-500 animate-pulse"></div>
                          <p className="text-sm text-slate-700 dark:text-slate-300">Strands and sub-strands</p>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="w-1.5 h-1.5 rounded-full bg-purple-500 animate-pulse"></div>
                          <p className="text-sm text-slate-700 dark:text-slate-300">Learning outcomes</p>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="w-1.5 h-1.5 rounded-full bg-indigo-500 animate-pulse"></div>
                          <p className="text-sm text-slate-700 dark:text-slate-300">Assessment strategies</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          <Card
            className={`border-2 border-dashed transition-all duration-300 cursor-pointer backdrop-blur-md rounded-xl shadow-lg ${
              dragOver
                ? "border-blue-500 bg-blue-50/20 dark:bg-blue-900/20 shadow-blue-500/20 scale-[1.02]"
                : "border-slate-300/70 dark:border-slate-700/70 hover:border-blue-400/70 dark:hover:border-blue-400/40 bg-white/30 dark:bg-slate-800/30 hover:bg-white/40 dark:hover:bg-slate-800/40"
            }`}
            onDrop={handleDrop}
            onDragOver={(e) => {
              e.preventDefault();
              setDragOver(true);
            }}
            onDragLeave={() => setDragOver(false)}
            onClick={() => fileInputRef.current?.click()}
          >
            <CardContent className="p-10 text-center">
              <div className={`relative mx-auto w-20 h-20 mb-6 ${dragOver ? 'animate-bounce-gentle' : ''}`}>
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-purple-500/20 dark:from-blue-500/10 dark:to-purple-500/10 rounded-full blur-xl"></div>
                <div className={`relative flex items-center justify-center w-full h-full rounded-xl bg-gradient-to-br ${
                  dragOver 
                    ? 'from-blue-500 to-indigo-500' 
                    : 'from-slate-100 to-slate-200 dark:from-slate-800 dark:to-slate-700'
                  } shadow-md transition-all duration-300`}>
                  <Upload
                    className={`h-10 w-10 ${
                      dragOver ? "text-white" : "text-slate-500 dark:text-slate-300"
                    }`}
                  />
                </div>
              </div>
              
              <h3 className={`text-xl font-semibold mb-2 transition-colors ${
                dragOver 
                  ? 'text-blue-600 dark:text-blue-400' 
                  : 'text-slate-800 dark:text-slate-200'
              }`}>
                {dragOver ? 'Release to Upload File' : 'Drop your scheme of work file here'}
              </h3>
              
              <p className="text-slate-600 dark:text-slate-400 mb-2">
                Supports PDF, Word, Excel, and text files
              </p>
              
              <p className="text-xs text-slate-500 dark:text-slate-500 mb-5">
                {acceptedFormats.join(", ")}
              </p>

              {uploadedFileName && (
                <div className="mb-6 p-3 bg-white/50 dark:bg-slate-800/50 backdrop-blur-md rounded-lg border border-blue-100 dark:border-blue-900/20 shadow-inner animate-fade-in">
                  <div className="flex items-center justify-center space-x-2 text-blue-600 dark:text-blue-400">
                    {getFileIcon(uploadedFileName)}
                    <span className="text-sm font-medium">
                      {uploadedFileName}
                    </span>
                  </div>
                </div>
              )}

              <Button
                variant="glass"
                size="lg"
                className="mt-2 group"
                onClick={(e) => {
                  e.stopPropagation();
                  fileInputRef.current?.click();
                }}
              >
                <FileText className="h-4 w-4 mr-2 group-hover:scale-110 transition-transform" />
                <span>Select File</span>
              </Button>
              
              {dragOver && (
                <div className="absolute inset-0 border-2 border-blue-500 rounded-xl border-dashed animate-pulse pointer-events-none"></div>
              )}
            </CardContent>
          </Card>

          <div className="space-y-4 mt-8 relative">
            <div className="absolute -top-12 -right-12 w-32 h-32 bg-gradient-to-br from-indigo-500/10 to-purple-500/10 dark:from-indigo-500/5 dark:to-purple-500/5 rounded-full blur-2xl"></div>
            <div className="absolute -bottom-12 -left-12 w-32 h-32 bg-gradient-to-tr from-blue-500/10 to-cyan-500/10 dark:from-blue-500/5 dark:to-cyan-500/5 rounded-full blur-2xl"></div>
            
            <div className="flex items-center gap-2 relative">
              <div className="bg-gradient-to-br from-indigo-100 to-blue-100 dark:from-indigo-900/30 dark:to-blue-900/30 p-2 rounded-lg shadow-inner">
                <ClipboardPaste className="h-4 w-4 text-indigo-600 dark:text-indigo-400" />
              </div>
              <h3 className="text-sm font-medium text-slate-700 dark:text-slate-300">
                Or paste your scheme of work content directly:
              </h3>
            </div>
            
            <Textarea
              placeholder="Paste your scheme of work content here..."
              value={textContent}
              onChange={(e) => setTextContent(e.target.value)}
              className="min-h-32 resize-none backdrop-blur-md bg-white/50 dark:bg-slate-800/50 border border-slate-200 dark:border-slate-700 shadow-inner rounded-xl text-slate-700 dark:text-slate-200 placeholder:text-slate-400 dark:placeholder:text-slate-500 focus:border-blue-400 focus:ring-blue-400/30"
            />
            
            <Button
              onClick={handleTextSubmit}
              disabled={!textContent.trim() || isProcessing}
              variant="primary"
              className="w-full relative overflow-hidden group"
            >
              <span className="relative z-10 flex items-center justify-center gap-2">
                {isProcessing ? (
                  <>
                    <Zap className="h-4 w-4 animate-pulse" />
                    <span>Parsing Content...</span>
                  </>
                ) : (
                  <>
                    <Zap className="h-4 w-4 group-hover:rotate-12 transition-transform" />
                    <span>Parse & Generate Lesson Plans</span>
                  </>
                )}
              </span>
              <span className="absolute inset-0 bg-gradient-to-r from-blue-600 to-indigo-600 opacity-0 group-hover:opacity-100 transition-opacity"></span>
            </Button>
          </div>
        </div>
      )}

      <input
        ref={fileInputRef}
        type="file"
        accept={acceptedFormats.join(",")}
        onChange={handleFileInputChange}
        className="hidden"
      />
    </div>
  );
};
