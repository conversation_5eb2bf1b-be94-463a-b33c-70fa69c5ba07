"""
Test Qwen 3.5 Efficiency for CBC Lesson Plan Generation
Demonstrates the MoE architecture advantages for document processing
"""

import asyncio
import time
import sys
import os

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from qwen_optimized_processor import QwenOptimizedProcessor
from optimized_file_processor import OptimizedFileProcessor

async def test_qwen_efficiency():
    """Test Qwen 3.5's efficiency on STM2025.pdf"""
    
    print("🧪 Testing Qwen 3.5 MoE Efficiency for CBC Processing")
    print("=" * 60)
    
    # Initialize processors
    file_processor = OptimizedFileProcessor()
    qwen_processor = QwenOptimizedProcessor()
    
    try:
        # Extract PDF content
        print("📄 Extracting STM2025.pdf content...")
        start_time = time.time()

        # Read the PDF file
        with open("STM2025.pdf", "rb") as f:
            file_content = f.read()

        content, metadata = file_processor.extract_text_fast(file_content, "STM2025.pdf", "test_001")
        extraction_time = time.time() - start_time
        
        print(f"✅ PDF extracted in {extraction_time:.2f} seconds")
        print(f"📊 Content length: {len(content):,} characters")
        print()
        
        # Test 1: Quick document analysis (like the 30-second benchmark)
        print("🔍 Test 1: Quick Document Structure Analysis")
        print("-" * 40)
        
        analysis_start = time.time()
        analysis = await qwen_processor.analyze_document_structure(content)
        analysis_time = time.time() - analysis_start
        
        print(f"⚡ Analysis completed in {analysis_time:.1f} seconds")
        print(f"📋 Results: {analysis}")
        print()
        
        # Test 2: Full lesson plan generation
        print("📚 Test 2: Full Lesson Plan Generation")
        print("-" * 40)
        
        processing_start = time.time()
        lesson_plans = await qwen_processor.process_scheme_with_qwen(content, "STM2025.pdf")
        processing_time = time.time() - processing_start
        
        print(f"🚀 Full processing completed in {processing_time:.1f} seconds")
        
        if 'analysis' in lesson_plans:
            analysis_data = lesson_plans['analysis']
            print(f"📊 Analysis Results:")
            print(f"   - Weeks: {analysis_data.get('total_weeks', 'N/A')}")
            print(f"   - Lessons per week: {analysis_data.get('lessons_per_week', 'N/A')}")
            print(f"   - Total lessons: {analysis_data.get('total_lessons', 'N/A')}")
            print(f"   - Subject: {analysis_data.get('subject', 'N/A')}")
            print(f"   - Grade: {analysis_data.get('grade', 'N/A')}")
        
        if 'lesson_plans' in lesson_plans:
            lesson_count = len(lesson_plans['lesson_plans'])
            print(f"📝 Generated {lesson_count} lesson plans")
            
            if lesson_count > 0:
                print(f"📖 Sample lesson plan:")
                sample = lesson_plans['lesson_plans'][0]
                print(f"   - Week {sample.get('week', 'N/A')}, Lesson {sample.get('lesson', 'N/A')}")
                print(f"   - Title: {sample.get('title', 'N/A')}")
                print(f"   - Strand: {sample.get('strand', 'N/A')}")
        
        print()
        
        # Performance comparison
        print("⚡ Performance Analysis")
        print("-" * 40)
        print(f"🏃 Quick Analysis: {analysis_time:.1f}s (Target: ~30s)")
        print(f"🚀 Full Processing: {processing_time:.1f}s")
        print(f"📈 Total Time: {analysis_time + processing_time:.1f}s")
        
        # Efficiency metrics
        if processing_time > 0:
            chars_per_second = len(content) / processing_time
            print(f"📊 Processing Speed: {chars_per_second:,.0f} chars/second")
        
        # Architecture benefits
        print()
        print("🏗️ Qwen 3.5 MoE Architecture Benefits")
        print("-" * 40)
        print("✨ 235B total parameters, 22B activated (9.4% efficiency)")
        print("⚡ 4.3× faster inference vs dense models")
        print("🧠 128K context window for large documents")
        print("🎯 Optimized for document processing tasks")
        print("💰 Completely free via OpenRouter")
        
        # Save results
        if 'error' not in lesson_plans:
            output_file = f"qwen_test_results_{int(time.time())}.json"
            import json
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(lesson_plans, f, indent=2, ensure_ascii=False)
            print(f"💾 Results saved to {output_file}")
        
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        await qwen_processor.close()

def compare_with_benchmark():
    """Compare our results with the Qwen 3.5 benchmark"""
    print()
    print("🏆 Benchmark Comparison")
    print("=" * 60)
    print("📊 Qwen 3.5 Benchmark (STM2025.pdf):")
    print("   - Processing Time: 30 seconds")
    print("   - Weeks Found: 11")
    print("   - Lessons per Week: 4")
    print("   - Total Lessons: 44")
    print("   - Accuracy: Perfect")
    print()
    print("🎯 Our Goal: Match or exceed this performance")
    print("💡 Key: Leverage MoE architecture efficiency")

if __name__ == "__main__":
    print("🔬 Qwen 3.5 MoE Efficiency Test")
    print("Testing CBC lesson plan generation performance")
    print()
    
    compare_with_benchmark()
    
    # Run the efficiency test
    asyncio.run(test_qwen_efficiency())
    
    print()
    print("✅ Test completed!")
    print("🚀 Ready to implement Qwen 3.5 efficiency in production!")
