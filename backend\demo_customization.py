#!/usr/bin/env python3
"""
Quick Demo of Teacher Customization Features
Shows how lesson plans are customized for different teacher contexts
"""

import sys
import json
sys.path.append('.')

from teacher_customization import TeacherCustomizer, TeacherProfile

def demo_customization():
    print("🎓 TEACHER CUSTOMIZATION DEMO")
    print("=" * 60)
    
    # Sample base lesson plan (like what comes from AI processing)
    base_lesson = {
        "week": 1,
        "lessonNumber": 1,
        "strand": "Living Things",
        "sub_strand": "Skeleton and Muscles",
        "specific_learning_outcomes": [
            "State the parts of a human beings skeleton",
            "Describe the functions of the skeleton in human beings"
        ],
        "key_inquiry_question": "What are the main parts of the human skeleton?",
        "learning_experiences": [
            "Observe skeleton model",
            "Draw skeleton parts"
        ],
        "learning_resources": [
            "Skeleton model",
            "Charts"
        ],
        "assessment": "Oral questions"
    }
    
    # Create different teacher profiles
    teachers = {
        "Urban Public Teacher": TeacherProfile(
            teacher_name="Mrs. <PERSON>",
            school_name="Nairobi Central Primary",
            county="Nairobi",
            class_size=45,
            teaching_style="Interactive",
            has_projector=False,
            has_computers=False,
            include_local_examples=True
        ),
        
        "Rural Practical Teacher": TeacherProfile(
            teacher_name="Mr. <PERSON> Kipchoge",
            school_name="Eldoret Hills Primary",
            county="Eldoret",
            class_size=25,
            teaching_style="Practical",
            outdoor_space=True,
            include_local_examples=True,
            preferred_activities=["Field work", "Hands-on activities"]
        ),
        
        "Private School Teacher": TeacherProfile(
            teacher_name="Ms. Sarah Akinyi",
            school_name="Kisumu International Academy",
            county="Kisumu",
            class_size=18,
            teaching_style="Inquiry-based",
            has_projector=True,
            has_computers=True,
            has_internet=True,
            has_science_lab=True,
            difficulty_level="Advanced"
        ),
        
        "Special Needs Teacher": TeacherProfile(
            teacher_name="Mrs. Mary Njeri",
            school_name="Inclusive Learning Center",
            county="Nakuru",
            class_size=12,
            teaching_style="Interactive",
            has_special_needs_students=True,
            special_needs_accommodations=[
                "Visual learning aids",
                "Simplified instructions",
                "Extra time allowance"
            ],
            difficulty_level="Basic"
        )
    }
    
    customizer = TeacherCustomizer()
    
    print("📋 BASE LESSON PLAN:")
    print("-" * 40)
    print(f"Strand: {base_lesson['strand']}")
    print(f"Sub-strand: {base_lesson['sub_strand']}")
    print(f"Learning Experiences: {', '.join(base_lesson['learning_experiences'])}")
    print(f"Resources: {', '.join(base_lesson['learning_resources'])}")
    print(f"Assessment: {base_lesson['assessment']}")
    print()
    
    # Show customization for each teacher
    for teacher_type, profile in teachers.items():
        print(f"👩‍🏫 {teacher_type.upper()}")
        print("=" * 60)
        
        # Customize the lesson
        customized = customizer.customize_lesson_plan(base_lesson, profile)
        
        print(f"🏫 School: {customized.get('school', 'N/A')}")
        print(f"👩‍🏫 Teacher: {customized.get('teacher', 'N/A')}")
        print(f"📍 Location: {profile.county}")
        print(f"👥 Class Size: {customized.get('class_size', 'N/A')} students")
        print(f"⏰ Duration: {customized.get('duration', 'N/A')}")
        print(f"🎯 Teaching Style: {profile.teaching_style}")
        print()
        
        # Enhanced learning experiences
        experiences = customized.get('learning_experiences', [])
        print(f"🎭 CUSTOMIZED LEARNING EXPERIENCES:")
        for i, exp in enumerate(experiences, 1):
            print(f"   {i}. {exp}")
        print()
        
        # Enhanced resources
        resources = customized.get('learning_resources', [])
        print(f"📦 CUSTOMIZED RESOURCES:")
        for i, res in enumerate(resources, 1):
            print(f"   {i}. {res}")
        print()
        
        # Assessment methods
        assessment = customized.get('assessment', 'N/A')
        additional = customized.get('additional_assessments', [])
        print(f"📝 ASSESSMENT:")
        print(f"   Primary: {assessment}")
        if additional:
            print(f"   Additional: {', '.join(additional)}")
        print()
        
        # Local examples
        local_examples = customized.get('local_examples', [])
        if local_examples:
            print(f"🌍 LOCAL EXAMPLES ({profile.county}):")
            for example in local_examples:
                print(f"   • {example}")
            print()
        
        # Career connections
        careers = customized.get('career_connections', [])
        if careers:
            print(f"💼 CAREER CONNECTIONS:")
            for career in careers:
                print(f"   • {career}")
            print()
        
        # Cross-curricular connections
        cross_curr = customized.get('cross_curricular', {})
        if cross_curr:
            print(f"🔗 CROSS-CURRICULAR CONNECTIONS:")
            for subject, connections in cross_curr.items():
                print(f"   {subject}: {', '.join(connections[:2])}")
            print()
        
        # Special accommodations
        accommodations = customized.get('special_needs_accommodations', [])
        inclusive_strategies = customized.get('inclusive_strategies', [])
        if accommodations or inclusive_strategies:
            print(f"♿ SPECIAL NEEDS SUPPORT:")
            for acc in accommodations:
                print(f"   • {acc}")
            for strategy in inclusive_strategies:
                print(f"   • {strategy}")
            print()
        
        # Difficulty adjustments
        difficulty = customized.get('difficulty_adjustment', {})
        if difficulty:
            print(f"📊 DIFFICULTY ADJUSTMENT ({profile.difficulty_level}):")
            for key, value in difficulty.items():
                print(f"   {key.title()}: {value}")
            print()
        
        print("-" * 60)
        print()
    
    print("🎉 CUSTOMIZATION DEMO COMPLETE!")
    print("✅ Each teacher gets lesson plans tailored to their specific context:")
    print("   • School type and resources available")
    print("   • Teaching style preferences")
    print("   • Local examples from their county")
    print("   • Class size and student needs")
    print("   • Technology integration level")
    print("   • Special needs accommodations")
    print("   • Career connections and cross-curricular links")

if __name__ == "__main__":
    demo_customization()
