"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tanstack";
exports.ids = ["vendor-chunks/@tanstack"];
exports.modules = {

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/focusManager.js":
/*!************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/focusManager.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FocusManager: () => (/* binding */ FocusManager),\n/* harmony export */   focusManager: () => (/* binding */ focusManager)\n/* harmony export */ });\n/* harmony import */ var _subscribable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/focusManager.ts\n\n\nvar FocusManager = class extends _subscribable_js__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n  #focused;\n  #cleanup;\n  #setup;\n  constructor() {\n    super();\n    this.#setup = (onFocus) => {\n      if (!_utils_js__WEBPACK_IMPORTED_MODULE_1__.isServer && window.addEventListener) {\n        const listener = () => onFocus();\n        window.addEventListener(\"visibilitychange\", listener, false);\n        return () => {\n          window.removeEventListener(\"visibilitychange\", listener);\n        };\n      }\n      return;\n    };\n  }\n  onSubscribe() {\n    if (!this.#cleanup) {\n      this.setEventListener(this.#setup);\n    }\n  }\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.#cleanup?.();\n      this.#cleanup = void 0;\n    }\n  }\n  setEventListener(setup) {\n    this.#setup = setup;\n    this.#cleanup?.();\n    this.#cleanup = setup((focused) => {\n      if (typeof focused === \"boolean\") {\n        this.setFocused(focused);\n      } else {\n        this.onFocus();\n      }\n    });\n  }\n  setFocused(focused) {\n    const changed = this.#focused !== focused;\n    if (changed) {\n      this.#focused = focused;\n      this.onFocus();\n    }\n  }\n  onFocus() {\n    const isFocused = this.isFocused();\n    this.listeners.forEach((listener) => {\n      listener(isFocused);\n    });\n  }\n  isFocused() {\n    if (typeof this.#focused === \"boolean\") {\n      return this.#focused;\n    }\n    return globalThis.document?.visibilityState !== \"hidden\";\n  }\n};\nvar focusManager = new FocusManager();\n\n//# sourceMappingURL=focusManager.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/focusManager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hasNextPage: () => (/* binding */ hasNextPage),\n/* harmony export */   hasPreviousPage: () => (/* binding */ hasPreviousPage),\n/* harmony export */   infiniteQueryBehavior: () => (/* binding */ infiniteQueryBehavior)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/infiniteQueryBehavior.ts\n\nfunction infiniteQueryBehavior(pages) {\n  return {\n    onFetch: (context, query) => {\n      const options = context.options;\n      const direction = context.fetchOptions?.meta?.fetchMore?.direction;\n      const oldPages = context.state.data?.pages || [];\n      const oldPageParams = context.state.data?.pageParams || [];\n      let result = { pages: [], pageParams: [] };\n      let currentPage = 0;\n      const fetchFn = async () => {\n        let cancelled = false;\n        const addSignalProperty = (object) => {\n          Object.defineProperty(object, \"signal\", {\n            enumerable: true,\n            get: () => {\n              if (context.signal.aborted) {\n                cancelled = true;\n              } else {\n                context.signal.addEventListener(\"abort\", () => {\n                  cancelled = true;\n                });\n              }\n              return context.signal;\n            }\n          });\n        };\n        const queryFn = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureQueryFn)(context.options, context.fetchOptions);\n        const fetchPage = async (data, param, previous) => {\n          if (cancelled) {\n            return Promise.reject();\n          }\n          if (param == null && data.pages.length) {\n            return Promise.resolve(data);\n          }\n          const queryFnContext = {\n            queryKey: context.queryKey,\n            pageParam: param,\n            direction: previous ? \"backward\" : \"forward\",\n            meta: context.options.meta\n          };\n          addSignalProperty(queryFnContext);\n          const page = await queryFn(\n            queryFnContext\n          );\n          const { maxPages } = context.options;\n          const addTo = previous ? _utils_js__WEBPACK_IMPORTED_MODULE_0__.addToStart : _utils_js__WEBPACK_IMPORTED_MODULE_0__.addToEnd;\n          return {\n            pages: addTo(data.pages, page, maxPages),\n            pageParams: addTo(data.pageParams, param, maxPages)\n          };\n        };\n        if (direction && oldPages.length) {\n          const previous = direction === \"backward\";\n          const pageParamFn = previous ? getPreviousPageParam : getNextPageParam;\n          const oldData = {\n            pages: oldPages,\n            pageParams: oldPageParams\n          };\n          const param = pageParamFn(options, oldData);\n          result = await fetchPage(oldData, param, previous);\n        } else {\n          const remainingPages = pages ?? oldPages.length;\n          do {\n            const param = currentPage === 0 ? oldPageParams[0] ?? options.initialPageParam : getNextPageParam(options, result);\n            if (currentPage > 0 && param == null) {\n              break;\n            }\n            result = await fetchPage(result, param);\n            currentPage++;\n          } while (currentPage < remainingPages);\n        }\n        return result;\n      };\n      if (context.options.persister) {\n        context.fetchFn = () => {\n          return context.options.persister?.(\n            fetchFn,\n            {\n              queryKey: context.queryKey,\n              meta: context.options.meta,\n              signal: context.signal\n            },\n            query\n          );\n        };\n      } else {\n        context.fetchFn = fetchFn;\n      }\n    }\n  };\n}\nfunction getNextPageParam(options, { pages, pageParams }) {\n  const lastIndex = pages.length - 1;\n  return pages.length > 0 ? options.getNextPageParam(\n    pages[lastIndex],\n    pages,\n    pageParams[lastIndex],\n    pageParams\n  ) : void 0;\n}\nfunction getPreviousPageParam(options, { pages, pageParams }) {\n  return pages.length > 0 ? options.getPreviousPageParam?.(pages[0], pages, pageParams[0], pageParams) : void 0;\n}\nfunction hasNextPage(options, data) {\n  if (!data)\n    return false;\n  return getNextPageParam(options, data) != null;\n}\nfunction hasPreviousPage(options, data) {\n  if (!data || !options.getPreviousPageParam)\n    return false;\n  return getPreviousPageParam(options, data) != null;\n}\n\n//# sourceMappingURL=infiniteQueryBehavior.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/mutation.js":
/*!********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/mutation.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Mutation: () => (/* binding */ Mutation),\n/* harmony export */   getDefaultState: () => (/* binding */ getDefaultState)\n/* harmony export */ });\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _removable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./removable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/removable.js\");\n/* harmony import */ var _retryer_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./retryer.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/retryer.js\");\n// src/mutation.ts\n\n\n\nvar Mutation = class extends _removable_js__WEBPACK_IMPORTED_MODULE_0__.Removable {\n  #observers;\n  #mutationCache;\n  #retryer;\n  constructor(config) {\n    super();\n    this.mutationId = config.mutationId;\n    this.#mutationCache = config.mutationCache;\n    this.#observers = [];\n    this.state = config.state || getDefaultState();\n    this.setOptions(config.options);\n    this.scheduleGc();\n  }\n  setOptions(options) {\n    this.options = options;\n    this.updateGcTime(this.options.gcTime);\n  }\n  get meta() {\n    return this.options.meta;\n  }\n  addObserver(observer) {\n    if (!this.#observers.includes(observer)) {\n      this.#observers.push(observer);\n      this.clearGcTimeout();\n      this.#mutationCache.notify({\n        type: \"observerAdded\",\n        mutation: this,\n        observer\n      });\n    }\n  }\n  removeObserver(observer) {\n    this.#observers = this.#observers.filter((x) => x !== observer);\n    this.scheduleGc();\n    this.#mutationCache.notify({\n      type: \"observerRemoved\",\n      mutation: this,\n      observer\n    });\n  }\n  optionalRemove() {\n    if (!this.#observers.length) {\n      if (this.state.status === \"pending\") {\n        this.scheduleGc();\n      } else {\n        this.#mutationCache.remove(this);\n      }\n    }\n  }\n  continue() {\n    return this.#retryer?.continue() ?? // continuing a mutation assumes that variables are set, mutation must have been dehydrated before\n    this.execute(this.state.variables);\n  }\n  async execute(variables) {\n    this.#retryer = (0,_retryer_js__WEBPACK_IMPORTED_MODULE_1__.createRetryer)({\n      fn: () => {\n        if (!this.options.mutationFn) {\n          return Promise.reject(new Error(\"No mutationFn found\"));\n        }\n        return this.options.mutationFn(variables);\n      },\n      onFail: (failureCount, error) => {\n        this.#dispatch({ type: \"failed\", failureCount, error });\n      },\n      onPause: () => {\n        this.#dispatch({ type: \"pause\" });\n      },\n      onContinue: () => {\n        this.#dispatch({ type: \"continue\" });\n      },\n      retry: this.options.retry ?? 0,\n      retryDelay: this.options.retryDelay,\n      networkMode: this.options.networkMode,\n      canRun: () => this.#mutationCache.canRun(this)\n    });\n    const restored = this.state.status === \"pending\";\n    const isPaused = !this.#retryer.canStart();\n    try {\n      if (!restored) {\n        this.#dispatch({ type: \"pending\", variables, isPaused });\n        await this.#mutationCache.config.onMutate?.(\n          variables,\n          this\n        );\n        const context = await this.options.onMutate?.(variables);\n        if (context !== this.state.context) {\n          this.#dispatch({\n            type: \"pending\",\n            context,\n            variables,\n            isPaused\n          });\n        }\n      }\n      const data = await this.#retryer.start();\n      await this.#mutationCache.config.onSuccess?.(\n        data,\n        variables,\n        this.state.context,\n        this\n      );\n      await this.options.onSuccess?.(data, variables, this.state.context);\n      await this.#mutationCache.config.onSettled?.(\n        data,\n        null,\n        this.state.variables,\n        this.state.context,\n        this\n      );\n      await this.options.onSettled?.(data, null, variables, this.state.context);\n      this.#dispatch({ type: \"success\", data });\n      return data;\n    } catch (error) {\n      try {\n        await this.#mutationCache.config.onError?.(\n          error,\n          variables,\n          this.state.context,\n          this\n        );\n        await this.options.onError?.(\n          error,\n          variables,\n          this.state.context\n        );\n        await this.#mutationCache.config.onSettled?.(\n          void 0,\n          error,\n          this.state.variables,\n          this.state.context,\n          this\n        );\n        await this.options.onSettled?.(\n          void 0,\n          error,\n          variables,\n          this.state.context\n        );\n        throw error;\n      } finally {\n        this.#dispatch({ type: \"error\", error });\n      }\n    } finally {\n      this.#mutationCache.runNext(this);\n    }\n  }\n  #dispatch(action) {\n    const reducer = (state) => {\n      switch (action.type) {\n        case \"failed\":\n          return {\n            ...state,\n            failureCount: action.failureCount,\n            failureReason: action.error\n          };\n        case \"pause\":\n          return {\n            ...state,\n            isPaused: true\n          };\n        case \"continue\":\n          return {\n            ...state,\n            isPaused: false\n          };\n        case \"pending\":\n          return {\n            ...state,\n            context: action.context,\n            data: void 0,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            isPaused: action.isPaused,\n            status: \"pending\",\n            variables: action.variables,\n            submittedAt: Date.now()\n          };\n        case \"success\":\n          return {\n            ...state,\n            data: action.data,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            status: \"success\",\n            isPaused: false\n          };\n        case \"error\":\n          return {\n            ...state,\n            data: void 0,\n            error: action.error,\n            failureCount: state.failureCount + 1,\n            failureReason: action.error,\n            isPaused: false,\n            status: \"error\"\n          };\n      }\n    };\n    this.state = reducer(this.state);\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(() => {\n      this.#observers.forEach((observer) => {\n        observer.onMutationUpdate(action);\n      });\n      this.#mutationCache.notify({\n        mutation: this,\n        type: \"updated\",\n        action\n      });\n    });\n  }\n};\nfunction getDefaultState() {\n  return {\n    context: void 0,\n    data: void 0,\n    error: null,\n    failureCount: 0,\n    failureReason: null,\n    isPaused: false,\n    status: \"idle\",\n    variables: void 0,\n    submittedAt: 0\n  };\n}\n\n//# sourceMappingURL=mutation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/mutation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/mutationCache.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/mutationCache.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MutationCache: () => (/* binding */ MutationCache)\n/* harmony export */ });\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _mutation_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mutation.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/mutation.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _subscribable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js\");\n// src/mutationCache.ts\n\n\n\n\nvar MutationCache = class extends _subscribable_js__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n  constructor(config = {}) {\n    super();\n    this.config = config;\n    this.#mutations = /* @__PURE__ */ new Map();\n    this.#mutationId = Date.now();\n  }\n  #mutations;\n  #mutationId;\n  build(client, options, state) {\n    const mutation = new _mutation_js__WEBPACK_IMPORTED_MODULE_1__.Mutation({\n      mutationCache: this,\n      mutationId: ++this.#mutationId,\n      options: client.defaultMutationOptions(options),\n      state\n    });\n    this.add(mutation);\n    return mutation;\n  }\n  add(mutation) {\n    const scope = scopeFor(mutation);\n    const mutations = this.#mutations.get(scope) ?? [];\n    mutations.push(mutation);\n    this.#mutations.set(scope, mutations);\n    this.notify({ type: \"added\", mutation });\n  }\n  remove(mutation) {\n    const scope = scopeFor(mutation);\n    if (this.#mutations.has(scope)) {\n      const mutations = this.#mutations.get(scope)?.filter((x) => x !== mutation);\n      if (mutations) {\n        if (mutations.length === 0) {\n          this.#mutations.delete(scope);\n        } else {\n          this.#mutations.set(scope, mutations);\n        }\n      }\n    }\n    this.notify({ type: \"removed\", mutation });\n  }\n  canRun(mutation) {\n    const firstPendingMutation = this.#mutations.get(scopeFor(mutation))?.find((m) => m.state.status === \"pending\");\n    return !firstPendingMutation || firstPendingMutation === mutation;\n  }\n  runNext(mutation) {\n    const foundMutation = this.#mutations.get(scopeFor(mutation))?.find((m) => m !== mutation && m.state.isPaused);\n    return foundMutation?.continue() ?? Promise.resolve();\n  }\n  clear() {\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(() => {\n      this.getAll().forEach((mutation) => {\n        this.remove(mutation);\n      });\n    });\n  }\n  getAll() {\n    return [...this.#mutations.values()].flat();\n  }\n  find(filters) {\n    const defaultedFilters = { exact: true, ...filters };\n    return this.getAll().find(\n      (mutation) => (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.matchMutation)(defaultedFilters, mutation)\n    );\n  }\n  findAll(filters = {}) {\n    return this.getAll().filter((mutation) => (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.matchMutation)(filters, mutation));\n  }\n  notify(event) {\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(() => {\n      this.listeners.forEach((listener) => {\n        listener(event);\n      });\n    });\n  }\n  resumePausedMutations() {\n    const pausedMutations = this.getAll().filter((x) => x.state.isPaused);\n    return _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(\n      () => Promise.all(\n        pausedMutations.map((mutation) => mutation.continue().catch(_utils_js__WEBPACK_IMPORTED_MODULE_3__.noop))\n      )\n    );\n  }\n};\nfunction scopeFor(mutation) {\n  return mutation.options.scope?.id ?? String(mutation.mutationId);\n}\n\n//# sourceMappingURL=mutationCache.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/mutationCache.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/notifyManager.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createNotifyManager: () => (/* binding */ createNotifyManager),\n/* harmony export */   notifyManager: () => (/* binding */ notifyManager)\n/* harmony export */ });\n// src/notifyManager.ts\nfunction createNotifyManager() {\n  let queue = [];\n  let transactions = 0;\n  let notifyFn = (callback) => {\n    callback();\n  };\n  let batchNotifyFn = (callback) => {\n    callback();\n  };\n  let scheduleFn = (cb) => setTimeout(cb, 0);\n  const schedule = (callback) => {\n    if (transactions) {\n      queue.push(callback);\n    } else {\n      scheduleFn(() => {\n        notifyFn(callback);\n      });\n    }\n  };\n  const flush = () => {\n    const originalQueue = queue;\n    queue = [];\n    if (originalQueue.length) {\n      scheduleFn(() => {\n        batchNotifyFn(() => {\n          originalQueue.forEach((callback) => {\n            notifyFn(callback);\n          });\n        });\n      });\n    }\n  };\n  return {\n    batch: (callback) => {\n      let result;\n      transactions++;\n      try {\n        result = callback();\n      } finally {\n        transactions--;\n        if (!transactions) {\n          flush();\n        }\n      }\n      return result;\n    },\n    /**\n     * All calls to the wrapped function will be batched.\n     */\n    batchCalls: (callback) => {\n      return (...args) => {\n        schedule(() => {\n          callback(...args);\n        });\n      };\n    },\n    schedule,\n    /**\n     * Use this method to set a custom notify function.\n     * This can be used to for example wrap notifications with `React.act` while running tests.\n     */\n    setNotifyFunction: (fn) => {\n      notifyFn = fn;\n    },\n    /**\n     * Use this method to set a custom function to batch notifications together into a single tick.\n     * By default React Query will use the batch function provided by ReactDOM or React Native.\n     */\n    setBatchNotifyFunction: (fn) => {\n      batchNotifyFn = fn;\n    },\n    setScheduler: (fn) => {\n      scheduleFn = fn;\n    }\n  };\n}\nvar notifyManager = createNotifyManager();\n\n//# sourceMappingURL=notifyManager.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/onlineManager.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/onlineManager.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OnlineManager: () => (/* binding */ OnlineManager),\n/* harmony export */   onlineManager: () => (/* binding */ onlineManager)\n/* harmony export */ });\n/* harmony import */ var _subscribable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/onlineManager.ts\n\n\nvar OnlineManager = class extends _subscribable_js__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n  #online = true;\n  #cleanup;\n  #setup;\n  constructor() {\n    super();\n    this.#setup = (onOnline) => {\n      if (!_utils_js__WEBPACK_IMPORTED_MODULE_1__.isServer && window.addEventListener) {\n        const onlineListener = () => onOnline(true);\n        const offlineListener = () => onOnline(false);\n        window.addEventListener(\"online\", onlineListener, false);\n        window.addEventListener(\"offline\", offlineListener, false);\n        return () => {\n          window.removeEventListener(\"online\", onlineListener);\n          window.removeEventListener(\"offline\", offlineListener);\n        };\n      }\n      return;\n    };\n  }\n  onSubscribe() {\n    if (!this.#cleanup) {\n      this.setEventListener(this.#setup);\n    }\n  }\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.#cleanup?.();\n      this.#cleanup = void 0;\n    }\n  }\n  setEventListener(setup) {\n    this.#setup = setup;\n    this.#cleanup?.();\n    this.#cleanup = setup(this.setOnline.bind(this));\n  }\n  setOnline(online) {\n    const changed = this.#online !== online;\n    if (changed) {\n      this.#online = online;\n      this.listeners.forEach((listener) => {\n        listener(online);\n      });\n    }\n  }\n  isOnline() {\n    return this.#online;\n  }\n};\nvar onlineManager = new OnlineManager();\n\n//# sourceMappingURL=onlineManager.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/onlineManager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/query.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/query.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Query: () => (/* binding */ Query),\n/* harmony export */   fetchState: () => (/* binding */ fetchState)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _retryer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./retryer.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/retryer.js\");\n/* harmony import */ var _removable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./removable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/removable.js\");\n// src/query.ts\n\n\n\n\nvar Query = class extends _removable_js__WEBPACK_IMPORTED_MODULE_0__.Removable {\n  #initialState;\n  #revertState;\n  #cache;\n  #retryer;\n  #defaultOptions;\n  #abortSignalConsumed;\n  constructor(config) {\n    super();\n    this.#abortSignalConsumed = false;\n    this.#defaultOptions = config.defaultOptions;\n    this.setOptions(config.options);\n    this.observers = [];\n    this.#cache = config.cache;\n    this.queryKey = config.queryKey;\n    this.queryHash = config.queryHash;\n    this.#initialState = getDefaultState(this.options);\n    this.state = config.state ?? this.#initialState;\n    this.scheduleGc();\n  }\n  get meta() {\n    return this.options.meta;\n  }\n  get promise() {\n    return this.#retryer?.promise;\n  }\n  setOptions(options) {\n    this.options = { ...this.#defaultOptions, ...options };\n    this.updateGcTime(this.options.gcTime);\n  }\n  optionalRemove() {\n    if (!this.observers.length && this.state.fetchStatus === \"idle\") {\n      this.#cache.remove(this);\n    }\n  }\n  setData(newData, options) {\n    const data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.replaceData)(this.state.data, newData, this.options);\n    this.#dispatch({\n      data,\n      type: \"success\",\n      dataUpdatedAt: options?.updatedAt,\n      manual: options?.manual\n    });\n    return data;\n  }\n  setState(state, setStateOptions) {\n    this.#dispatch({ type: \"setState\", state, setStateOptions });\n  }\n  cancel(options) {\n    const promise = this.#retryer?.promise;\n    this.#retryer?.cancel(options);\n    return promise ? promise.then(_utils_js__WEBPACK_IMPORTED_MODULE_1__.noop).catch(_utils_js__WEBPACK_IMPORTED_MODULE_1__.noop) : Promise.resolve();\n  }\n  destroy() {\n    super.destroy();\n    this.cancel({ silent: true });\n  }\n  reset() {\n    this.destroy();\n    this.setState(this.#initialState);\n  }\n  isActive() {\n    return this.observers.some(\n      (observer) => (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.resolveEnabled)(observer.options.enabled, this) !== false\n    );\n  }\n  isDisabled() {\n    if (this.getObserversCount() > 0) {\n      return !this.isActive();\n    }\n    return this.options.queryFn === _utils_js__WEBPACK_IMPORTED_MODULE_1__.skipToken || this.state.dataUpdateCount + this.state.errorUpdateCount === 0;\n  }\n  isStale() {\n    if (this.state.isInvalidated) {\n      return true;\n    }\n    if (this.getObserversCount() > 0) {\n      return this.observers.some(\n        (observer) => observer.getCurrentResult().isStale\n      );\n    }\n    return this.state.data === void 0;\n  }\n  isStaleByTime(staleTime = 0) {\n    return this.state.isInvalidated || this.state.data === void 0 || !(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.timeUntilStale)(this.state.dataUpdatedAt, staleTime);\n  }\n  onFocus() {\n    const observer = this.observers.find((x) => x.shouldFetchOnWindowFocus());\n    observer?.refetch({ cancelRefetch: false });\n    this.#retryer?.continue();\n  }\n  onOnline() {\n    const observer = this.observers.find((x) => x.shouldFetchOnReconnect());\n    observer?.refetch({ cancelRefetch: false });\n    this.#retryer?.continue();\n  }\n  addObserver(observer) {\n    if (!this.observers.includes(observer)) {\n      this.observers.push(observer);\n      this.clearGcTimeout();\n      this.#cache.notify({ type: \"observerAdded\", query: this, observer });\n    }\n  }\n  removeObserver(observer) {\n    if (this.observers.includes(observer)) {\n      this.observers = this.observers.filter((x) => x !== observer);\n      if (!this.observers.length) {\n        if (this.#retryer) {\n          if (this.#abortSignalConsumed) {\n            this.#retryer.cancel({ revert: true });\n          } else {\n            this.#retryer.cancelRetry();\n          }\n        }\n        this.scheduleGc();\n      }\n      this.#cache.notify({ type: \"observerRemoved\", query: this, observer });\n    }\n  }\n  getObserversCount() {\n    return this.observers.length;\n  }\n  invalidate() {\n    if (!this.state.isInvalidated) {\n      this.#dispatch({ type: \"invalidate\" });\n    }\n  }\n  fetch(options, fetchOptions) {\n    if (this.state.fetchStatus !== \"idle\") {\n      if (this.state.data !== void 0 && fetchOptions?.cancelRefetch) {\n        this.cancel({ silent: true });\n      } else if (this.#retryer) {\n        this.#retryer.continueRetry();\n        return this.#retryer.promise;\n      }\n    }\n    if (options) {\n      this.setOptions(options);\n    }\n    if (!this.options.queryFn) {\n      const observer = this.observers.find((x) => x.options.queryFn);\n      if (observer) {\n        this.setOptions(observer.options);\n      }\n    }\n    if (true) {\n      if (!Array.isArray(this.options.queryKey)) {\n        console.error(\n          `As of v4, queryKey needs to be an Array. If you are using a string like 'repoData', please change it to an Array, e.g. ['repoData']`\n        );\n      }\n    }\n    const abortController = new AbortController();\n    const addSignalProperty = (object) => {\n      Object.defineProperty(object, \"signal\", {\n        enumerable: true,\n        get: () => {\n          this.#abortSignalConsumed = true;\n          return abortController.signal;\n        }\n      });\n    };\n    const fetchFn = () => {\n      const queryFn = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.ensureQueryFn)(this.options, fetchOptions);\n      const queryFnContext = {\n        queryKey: this.queryKey,\n        meta: this.meta\n      };\n      addSignalProperty(queryFnContext);\n      this.#abortSignalConsumed = false;\n      if (this.options.persister) {\n        return this.options.persister(\n          queryFn,\n          queryFnContext,\n          this\n        );\n      }\n      return queryFn(queryFnContext);\n    };\n    const context = {\n      fetchOptions,\n      options: this.options,\n      queryKey: this.queryKey,\n      state: this.state,\n      fetchFn\n    };\n    addSignalProperty(context);\n    this.options.behavior?.onFetch(\n      context,\n      this\n    );\n    this.#revertState = this.state;\n    if (this.state.fetchStatus === \"idle\" || this.state.fetchMeta !== context.fetchOptions?.meta) {\n      this.#dispatch({ type: \"fetch\", meta: context.fetchOptions?.meta });\n    }\n    const onError = (error) => {\n      if (!((0,_retryer_js__WEBPACK_IMPORTED_MODULE_2__.isCancelledError)(error) && error.silent)) {\n        this.#dispatch({\n          type: \"error\",\n          error\n        });\n      }\n      if (!(0,_retryer_js__WEBPACK_IMPORTED_MODULE_2__.isCancelledError)(error)) {\n        this.#cache.config.onError?.(\n          error,\n          this\n        );\n        this.#cache.config.onSettled?.(\n          this.state.data,\n          error,\n          this\n        );\n      }\n      this.scheduleGc();\n    };\n    this.#retryer = (0,_retryer_js__WEBPACK_IMPORTED_MODULE_2__.createRetryer)({\n      initialPromise: fetchOptions?.initialPromise,\n      fn: context.fetchFn,\n      abort: abortController.abort.bind(abortController),\n      onSuccess: (data) => {\n        if (data === void 0) {\n          if (true) {\n            console.error(\n              `Query data cannot be undefined. Please make sure to return a value other than undefined from your query function. Affected query key: ${this.queryHash}`\n            );\n          }\n          onError(new Error(`${this.queryHash} data is undefined`));\n          return;\n        }\n        try {\n          this.setData(data);\n        } catch (error) {\n          onError(error);\n          return;\n        }\n        this.#cache.config.onSuccess?.(data, this);\n        this.#cache.config.onSettled?.(\n          data,\n          this.state.error,\n          this\n        );\n        this.scheduleGc();\n      },\n      onError,\n      onFail: (failureCount, error) => {\n        this.#dispatch({ type: \"failed\", failureCount, error });\n      },\n      onPause: () => {\n        this.#dispatch({ type: \"pause\" });\n      },\n      onContinue: () => {\n        this.#dispatch({ type: \"continue\" });\n      },\n      retry: context.options.retry,\n      retryDelay: context.options.retryDelay,\n      networkMode: context.options.networkMode,\n      canRun: () => true\n    });\n    return this.#retryer.start();\n  }\n  #dispatch(action) {\n    const reducer = (state) => {\n      switch (action.type) {\n        case \"failed\":\n          return {\n            ...state,\n            fetchFailureCount: action.failureCount,\n            fetchFailureReason: action.error\n          };\n        case \"pause\":\n          return {\n            ...state,\n            fetchStatus: \"paused\"\n          };\n        case \"continue\":\n          return {\n            ...state,\n            fetchStatus: \"fetching\"\n          };\n        case \"fetch\":\n          return {\n            ...state,\n            ...fetchState(state.data, this.options),\n            fetchMeta: action.meta ?? null\n          };\n        case \"success\":\n          return {\n            ...state,\n            data: action.data,\n            dataUpdateCount: state.dataUpdateCount + 1,\n            dataUpdatedAt: action.dataUpdatedAt ?? Date.now(),\n            error: null,\n            isInvalidated: false,\n            status: \"success\",\n            ...!action.manual && {\n              fetchStatus: \"idle\",\n              fetchFailureCount: 0,\n              fetchFailureReason: null\n            }\n          };\n        case \"error\":\n          const error = action.error;\n          if ((0,_retryer_js__WEBPACK_IMPORTED_MODULE_2__.isCancelledError)(error) && error.revert && this.#revertState) {\n            return { ...this.#revertState, fetchStatus: \"idle\" };\n          }\n          return {\n            ...state,\n            error,\n            errorUpdateCount: state.errorUpdateCount + 1,\n            errorUpdatedAt: Date.now(),\n            fetchFailureCount: state.fetchFailureCount + 1,\n            fetchFailureReason: error,\n            fetchStatus: \"idle\",\n            status: \"error\"\n          };\n        case \"invalidate\":\n          return {\n            ...state,\n            isInvalidated: true\n          };\n        case \"setState\":\n          return {\n            ...state,\n            ...action.state\n          };\n      }\n    };\n    this.state = reducer(this.state);\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(() => {\n      this.observers.forEach((observer) => {\n        observer.onQueryUpdate();\n      });\n      this.#cache.notify({ query: this, type: \"updated\", action });\n    });\n  }\n};\nfunction fetchState(data, options) {\n  return {\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchStatus: (0,_retryer_js__WEBPACK_IMPORTED_MODULE_2__.canFetch)(options.networkMode) ? \"fetching\" : \"paused\",\n    ...data === void 0 && {\n      error: null,\n      status: \"pending\"\n    }\n  };\n}\nfunction getDefaultState(options) {\n  const data = typeof options.initialData === \"function\" ? options.initialData() : options.initialData;\n  const hasData = data !== void 0;\n  const initialDataUpdatedAt = hasData ? typeof options.initialDataUpdatedAt === \"function\" ? options.initialDataUpdatedAt() : options.initialDataUpdatedAt : 0;\n  return {\n    data,\n    dataUpdateCount: 0,\n    dataUpdatedAt: hasData ? initialDataUpdatedAt ?? Date.now() : 0,\n    error: null,\n    errorUpdateCount: 0,\n    errorUpdatedAt: 0,\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchMeta: null,\n    isInvalidated: false,\n    status: hasData ? \"success\" : \"pending\",\n    fetchStatus: \"idle\"\n  };\n}\n\n//# sourceMappingURL=query.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/query.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/queryCache.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/queryCache.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryCache: () => (/* binding */ QueryCache)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _query_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./query.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/query.js\");\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _subscribable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js\");\n// src/queryCache.ts\n\n\n\n\nvar QueryCache = class extends _subscribable_js__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n  constructor(config = {}) {\n    super();\n    this.config = config;\n    this.#queries = /* @__PURE__ */ new Map();\n  }\n  #queries;\n  build(client, options, state) {\n    const queryKey = options.queryKey;\n    const queryHash = options.queryHash ?? (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.hashQueryKeyByOptions)(queryKey, options);\n    let query = this.get(queryHash);\n    if (!query) {\n      query = new _query_js__WEBPACK_IMPORTED_MODULE_2__.Query({\n        cache: this,\n        queryKey,\n        queryHash,\n        options: client.defaultQueryOptions(options),\n        state,\n        defaultOptions: client.getQueryDefaults(queryKey)\n      });\n      this.add(query);\n    }\n    return query;\n  }\n  add(query) {\n    if (!this.#queries.has(query.queryHash)) {\n      this.#queries.set(query.queryHash, query);\n      this.notify({\n        type: \"added\",\n        query\n      });\n    }\n  }\n  remove(query) {\n    const queryInMap = this.#queries.get(query.queryHash);\n    if (queryInMap) {\n      query.destroy();\n      if (queryInMap === query) {\n        this.#queries.delete(query.queryHash);\n      }\n      this.notify({ type: \"removed\", query });\n    }\n  }\n  clear() {\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(() => {\n      this.getAll().forEach((query) => {\n        this.remove(query);\n      });\n    });\n  }\n  get(queryHash) {\n    return this.#queries.get(queryHash);\n  }\n  getAll() {\n    return [...this.#queries.values()];\n  }\n  find(filters) {\n    const defaultedFilters = { exact: true, ...filters };\n    return this.getAll().find(\n      (query) => (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.matchQuery)(defaultedFilters, query)\n    );\n  }\n  findAll(filters = {}) {\n    const queries = this.getAll();\n    return Object.keys(filters).length > 0 ? queries.filter((query) => (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.matchQuery)(filters, query)) : queries;\n  }\n  notify(event) {\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(() => {\n      this.listeners.forEach((listener) => {\n        listener(event);\n      });\n    });\n  }\n  onFocus() {\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(() => {\n      this.getAll().forEach((query) => {\n        query.onFocus();\n      });\n    });\n  }\n  onOnline() {\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(() => {\n      this.getAll().forEach((query) => {\n        query.onOnline();\n      });\n    });\n  }\n};\n\n//# sourceMappingURL=queryCache.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/queryCache.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/queryClient.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryClient: () => (/* binding */ QueryClient)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _queryCache_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./queryCache.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryCache.js\");\n/* harmony import */ var _mutationCache_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mutationCache.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/mutationCache.js\");\n/* harmony import */ var _focusManager_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./focusManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/focusManager.js\");\n/* harmony import */ var _onlineManager_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./onlineManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/onlineManager.js\");\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _infiniteQueryBehavior_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./infiniteQueryBehavior.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js\");\n// src/queryClient.ts\n\n\n\n\n\n\n\nvar QueryClient = class {\n  #queryCache;\n  #mutationCache;\n  #defaultOptions;\n  #queryDefaults;\n  #mutationDefaults;\n  #mountCount;\n  #unsubscribeFocus;\n  #unsubscribeOnline;\n  constructor(config = {}) {\n    this.#queryCache = config.queryCache || new _queryCache_js__WEBPACK_IMPORTED_MODULE_0__.QueryCache();\n    this.#mutationCache = config.mutationCache || new _mutationCache_js__WEBPACK_IMPORTED_MODULE_1__.MutationCache();\n    this.#defaultOptions = config.defaultOptions || {};\n    this.#queryDefaults = /* @__PURE__ */ new Map();\n    this.#mutationDefaults = /* @__PURE__ */ new Map();\n    this.#mountCount = 0;\n  }\n  mount() {\n    this.#mountCount++;\n    if (this.#mountCount !== 1)\n      return;\n    this.#unsubscribeFocus = _focusManager_js__WEBPACK_IMPORTED_MODULE_2__.focusManager.subscribe(async (focused) => {\n      if (focused) {\n        await this.resumePausedMutations();\n        this.#queryCache.onFocus();\n      }\n    });\n    this.#unsubscribeOnline = _onlineManager_js__WEBPACK_IMPORTED_MODULE_3__.onlineManager.subscribe(async (online) => {\n      if (online) {\n        await this.resumePausedMutations();\n        this.#queryCache.onOnline();\n      }\n    });\n  }\n  unmount() {\n    this.#mountCount--;\n    if (this.#mountCount !== 0)\n      return;\n    this.#unsubscribeFocus?.();\n    this.#unsubscribeFocus = void 0;\n    this.#unsubscribeOnline?.();\n    this.#unsubscribeOnline = void 0;\n  }\n  isFetching(filters) {\n    return this.#queryCache.findAll({ ...filters, fetchStatus: \"fetching\" }).length;\n  }\n  isMutating(filters) {\n    return this.#mutationCache.findAll({ ...filters, status: \"pending\" }).length;\n  }\n  getQueryData(queryKey) {\n    const options = this.defaultQueryOptions({ queryKey });\n    return this.#queryCache.get(options.queryHash)?.state.data;\n  }\n  ensureQueryData(options) {\n    const cachedData = this.getQueryData(options.queryKey);\n    if (cachedData === void 0)\n      return this.fetchQuery(options);\n    else {\n      const defaultedOptions = this.defaultQueryOptions(options);\n      const query = this.#queryCache.build(this, defaultedOptions);\n      if (options.revalidateIfStale && query.isStaleByTime((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.resolveStaleTime)(defaultedOptions.staleTime, query))) {\n        void this.prefetchQuery(defaultedOptions);\n      }\n      return Promise.resolve(cachedData);\n    }\n  }\n  getQueriesData(filters) {\n    return this.#queryCache.findAll(filters).map(({ queryKey, state }) => {\n      const data = state.data;\n      return [queryKey, data];\n    });\n  }\n  setQueryData(queryKey, updater, options) {\n    const defaultedOptions = this.defaultQueryOptions({ queryKey });\n    const query = this.#queryCache.get(\n      defaultedOptions.queryHash\n    );\n    const prevData = query?.state.data;\n    const data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.functionalUpdate)(updater, prevData);\n    if (data === void 0) {\n      return void 0;\n    }\n    return this.#queryCache.build(this, defaultedOptions).setData(data, { ...options, manual: true });\n  }\n  setQueriesData(filters, updater, options) {\n    return _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(\n      () => this.#queryCache.findAll(filters).map(({ queryKey }) => [\n        queryKey,\n        this.setQueryData(queryKey, updater, options)\n      ])\n    );\n  }\n  getQueryState(queryKey) {\n    const options = this.defaultQueryOptions({ queryKey });\n    return this.#queryCache.get(options.queryHash)?.state;\n  }\n  removeQueries(filters) {\n    const queryCache = this.#queryCache;\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach((query) => {\n        queryCache.remove(query);\n      });\n    });\n  }\n  resetQueries(filters, options) {\n    const queryCache = this.#queryCache;\n    const refetchFilters = {\n      type: \"active\",\n      ...filters\n    };\n    return _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach((query) => {\n        query.reset();\n      });\n      return this.refetchQueries(refetchFilters, options);\n    });\n  }\n  cancelQueries(filters = {}, cancelOptions = {}) {\n    const defaultedCancelOptions = { revert: true, ...cancelOptions };\n    const promises = _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(\n      () => this.#queryCache.findAll(filters).map((query) => query.cancel(defaultedCancelOptions))\n    );\n    return Promise.all(promises).then(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop).catch(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop);\n  }\n  invalidateQueries(filters = {}, options = {}) {\n    return _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(() => {\n      this.#queryCache.findAll(filters).forEach((query) => {\n        query.invalidate();\n      });\n      if (filters.refetchType === \"none\") {\n        return Promise.resolve();\n      }\n      const refetchFilters = {\n        ...filters,\n        type: filters.refetchType ?? filters.type ?? \"active\"\n      };\n      return this.refetchQueries(refetchFilters, options);\n    });\n  }\n  refetchQueries(filters = {}, options) {\n    const fetchOptions = {\n      ...options,\n      cancelRefetch: options?.cancelRefetch ?? true\n    };\n    const promises = _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(\n      () => this.#queryCache.findAll(filters).filter((query) => !query.isDisabled()).map((query) => {\n        let promise = query.fetch(void 0, fetchOptions);\n        if (!fetchOptions.throwOnError) {\n          promise = promise.catch(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop);\n        }\n        return query.state.fetchStatus === \"paused\" ? Promise.resolve() : promise;\n      })\n    );\n    return Promise.all(promises).then(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop);\n  }\n  fetchQuery(options) {\n    const defaultedOptions = this.defaultQueryOptions(options);\n    if (defaultedOptions.retry === void 0) {\n      defaultedOptions.retry = false;\n    }\n    const query = this.#queryCache.build(this, defaultedOptions);\n    return query.isStaleByTime(\n      (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.resolveStaleTime)(defaultedOptions.staleTime, query)\n    ) ? query.fetch(defaultedOptions) : Promise.resolve(query.state.data);\n  }\n  prefetchQuery(options) {\n    return this.fetchQuery(options).then(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop).catch(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop);\n  }\n  fetchInfiniteQuery(options) {\n    options.behavior = (0,_infiniteQueryBehavior_js__WEBPACK_IMPORTED_MODULE_6__.infiniteQueryBehavior)(options.pages);\n    return this.fetchQuery(options);\n  }\n  prefetchInfiniteQuery(options) {\n    return this.fetchInfiniteQuery(options).then(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop).catch(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop);\n  }\n  ensureInfiniteQueryData(options) {\n    options.behavior = (0,_infiniteQueryBehavior_js__WEBPACK_IMPORTED_MODULE_6__.infiniteQueryBehavior)(options.pages);\n    return this.ensureQueryData(options);\n  }\n  resumePausedMutations() {\n    if (_onlineManager_js__WEBPACK_IMPORTED_MODULE_3__.onlineManager.isOnline()) {\n      return this.#mutationCache.resumePausedMutations();\n    }\n    return Promise.resolve();\n  }\n  getQueryCache() {\n    return this.#queryCache;\n  }\n  getMutationCache() {\n    return this.#mutationCache;\n  }\n  getDefaultOptions() {\n    return this.#defaultOptions;\n  }\n  setDefaultOptions(options) {\n    this.#defaultOptions = options;\n  }\n  setQueryDefaults(queryKey, options) {\n    this.#queryDefaults.set((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.hashKey)(queryKey), {\n      queryKey,\n      defaultOptions: options\n    });\n  }\n  getQueryDefaults(queryKey) {\n    const defaults = [...this.#queryDefaults.values()];\n    let result = {};\n    defaults.forEach((queryDefault) => {\n      if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.partialMatchKey)(queryKey, queryDefault.queryKey)) {\n        result = { ...result, ...queryDefault.defaultOptions };\n      }\n    });\n    return result;\n  }\n  setMutationDefaults(mutationKey, options) {\n    this.#mutationDefaults.set((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.hashKey)(mutationKey), {\n      mutationKey,\n      defaultOptions: options\n    });\n  }\n  getMutationDefaults(mutationKey) {\n    const defaults = [...this.#mutationDefaults.values()];\n    let result = {};\n    defaults.forEach((queryDefault) => {\n      if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.partialMatchKey)(mutationKey, queryDefault.mutationKey)) {\n        result = { ...result, ...queryDefault.defaultOptions };\n      }\n    });\n    return result;\n  }\n  defaultQueryOptions(options) {\n    if (options._defaulted) {\n      return options;\n    }\n    const defaultedOptions = {\n      ...this.#defaultOptions.queries,\n      ...this.getQueryDefaults(options.queryKey),\n      ...options,\n      _defaulted: true\n    };\n    if (!defaultedOptions.queryHash) {\n      defaultedOptions.queryHash = (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.hashQueryKeyByOptions)(\n        defaultedOptions.queryKey,\n        defaultedOptions\n      );\n    }\n    if (defaultedOptions.refetchOnReconnect === void 0) {\n      defaultedOptions.refetchOnReconnect = defaultedOptions.networkMode !== \"always\";\n    }\n    if (defaultedOptions.throwOnError === void 0) {\n      defaultedOptions.throwOnError = !!defaultedOptions.suspense;\n    }\n    if (!defaultedOptions.networkMode && defaultedOptions.persister) {\n      defaultedOptions.networkMode = \"offlineFirst\";\n    }\n    if (defaultedOptions.enabled !== true && defaultedOptions.queryFn === _utils_js__WEBPACK_IMPORTED_MODULE_4__.skipToken) {\n      defaultedOptions.enabled = false;\n    }\n    return defaultedOptions;\n  }\n  defaultMutationOptions(options) {\n    if (options?._defaulted) {\n      return options;\n    }\n    return {\n      ...this.#defaultOptions.mutations,\n      ...options?.mutationKey && this.getMutationDefaults(options.mutationKey),\n      ...options,\n      _defaulted: true\n    };\n  }\n  clear() {\n    this.#queryCache.clear();\n    this.#mutationCache.clear();\n  }\n};\n\n//# sourceMappingURL=queryClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/removable.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/removable.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Removable: () => (/* binding */ Removable)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/removable.ts\n\nvar Removable = class {\n  #gcTimeout;\n  destroy() {\n    this.clearGcTimeout();\n  }\n  scheduleGc() {\n    this.clearGcTimeout();\n    if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.isValidTimeout)(this.gcTime)) {\n      this.#gcTimeout = setTimeout(() => {\n        this.optionalRemove();\n      }, this.gcTime);\n    }\n  }\n  updateGcTime(newGcTime) {\n    this.gcTime = Math.max(\n      this.gcTime || 0,\n      newGcTime ?? (_utils_js__WEBPACK_IMPORTED_MODULE_0__.isServer ? Infinity : 5 * 60 * 1e3)\n    );\n  }\n  clearGcTimeout() {\n    if (this.#gcTimeout) {\n      clearTimeout(this.#gcTimeout);\n      this.#gcTimeout = void 0;\n    }\n  }\n};\n\n//# sourceMappingURL=removable.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3F1ZXJ5LWNvcmUvYnVpbGQvbW9kZXJuL3JlbW92YWJsZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ3NEO0FBQ3REO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSx5REFBYztBQUN0QjtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsK0NBQVE7QUFDNUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBR0U7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtbGVzc29uLXBsYW4tZ2VuZXJhdG9yLy4vbm9kZV9tb2R1bGVzL0B0YW5zdGFjay9xdWVyeS1jb3JlL2J1aWxkL21vZGVybi9yZW1vdmFibGUuanM/Y2EzNCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvcmVtb3ZhYmxlLnRzXG5pbXBvcnQgeyBpc1NlcnZlciwgaXNWYWxpZFRpbWVvdXQgfSBmcm9tIFwiLi91dGlscy5qc1wiO1xudmFyIFJlbW92YWJsZSA9IGNsYXNzIHtcbiAgI2djVGltZW91dDtcbiAgZGVzdHJveSgpIHtcbiAgICB0aGlzLmNsZWFyR2NUaW1lb3V0KCk7XG4gIH1cbiAgc2NoZWR1bGVHYygpIHtcbiAgICB0aGlzLmNsZWFyR2NUaW1lb3V0KCk7XG4gICAgaWYgKGlzVmFsaWRUaW1lb3V0KHRoaXMuZ2NUaW1lKSkge1xuICAgICAgdGhpcy4jZ2NUaW1lb3V0ID0gc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgIHRoaXMub3B0aW9uYWxSZW1vdmUoKTtcbiAgICAgIH0sIHRoaXMuZ2NUaW1lKTtcbiAgICB9XG4gIH1cbiAgdXBkYXRlR2NUaW1lKG5ld0djVGltZSkge1xuICAgIHRoaXMuZ2NUaW1lID0gTWF0aC5tYXgoXG4gICAgICB0aGlzLmdjVGltZSB8fCAwLFxuICAgICAgbmV3R2NUaW1lID8/IChpc1NlcnZlciA/IEluZmluaXR5IDogNSAqIDYwICogMWUzKVxuICAgICk7XG4gIH1cbiAgY2xlYXJHY1RpbWVvdXQoKSB7XG4gICAgaWYgKHRoaXMuI2djVGltZW91dCkge1xuICAgICAgY2xlYXJUaW1lb3V0KHRoaXMuI2djVGltZW91dCk7XG4gICAgICB0aGlzLiNnY1RpbWVvdXQgPSB2b2lkIDA7XG4gICAgfVxuICB9XG59O1xuZXhwb3J0IHtcbiAgUmVtb3ZhYmxlXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cmVtb3ZhYmxlLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/removable.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/retryer.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/retryer.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CancelledError: () => (/* binding */ CancelledError),\n/* harmony export */   canFetch: () => (/* binding */ canFetch),\n/* harmony export */   createRetryer: () => (/* binding */ createRetryer),\n/* harmony export */   isCancelledError: () => (/* binding */ isCancelledError)\n/* harmony export */ });\n/* harmony import */ var _focusManager_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./focusManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/focusManager.js\");\n/* harmony import */ var _onlineManager_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./onlineManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/onlineManager.js\");\n/* harmony import */ var _thenable_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./thenable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/thenable.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/retryer.ts\n\n\n\n\nfunction defaultRetryDelay(failureCount) {\n  return Math.min(1e3 * 2 ** failureCount, 3e4);\n}\nfunction canFetch(networkMode) {\n  return (networkMode ?? \"online\") === \"online\" ? _onlineManager_js__WEBPACK_IMPORTED_MODULE_0__.onlineManager.isOnline() : true;\n}\nvar CancelledError = class extends Error {\n  constructor(options) {\n    super(\"CancelledError\");\n    this.revert = options?.revert;\n    this.silent = options?.silent;\n  }\n};\nfunction isCancelledError(value) {\n  return value instanceof CancelledError;\n}\nfunction createRetryer(config) {\n  let isRetryCancelled = false;\n  let failureCount = 0;\n  let isResolved = false;\n  let continueFn;\n  const thenable = (0,_thenable_js__WEBPACK_IMPORTED_MODULE_1__.pendingThenable)();\n  const cancel = (cancelOptions) => {\n    if (!isResolved) {\n      reject(new CancelledError(cancelOptions));\n      config.abort?.();\n    }\n  };\n  const cancelRetry = () => {\n    isRetryCancelled = true;\n  };\n  const continueRetry = () => {\n    isRetryCancelled = false;\n  };\n  const canContinue = () => _focusManager_js__WEBPACK_IMPORTED_MODULE_2__.focusManager.isFocused() && (config.networkMode === \"always\" || _onlineManager_js__WEBPACK_IMPORTED_MODULE_0__.onlineManager.isOnline()) && config.canRun();\n  const canStart = () => canFetch(config.networkMode) && config.canRun();\n  const resolve = (value) => {\n    if (!isResolved) {\n      isResolved = true;\n      config.onSuccess?.(value);\n      continueFn?.();\n      thenable.resolve(value);\n    }\n  };\n  const reject = (value) => {\n    if (!isResolved) {\n      isResolved = true;\n      config.onError?.(value);\n      continueFn?.();\n      thenable.reject(value);\n    }\n  };\n  const pause = () => {\n    return new Promise((continueResolve) => {\n      continueFn = (value) => {\n        if (isResolved || canContinue()) {\n          continueResolve(value);\n        }\n      };\n      config.onPause?.();\n    }).then(() => {\n      continueFn = void 0;\n      if (!isResolved) {\n        config.onContinue?.();\n      }\n    });\n  };\n  const run = () => {\n    if (isResolved) {\n      return;\n    }\n    let promiseOrValue;\n    const initialPromise = failureCount === 0 ? config.initialPromise : void 0;\n    try {\n      promiseOrValue = initialPromise ?? config.fn();\n    } catch (error) {\n      promiseOrValue = Promise.reject(error);\n    }\n    Promise.resolve(promiseOrValue).then(resolve).catch((error) => {\n      if (isResolved) {\n        return;\n      }\n      const retry = config.retry ?? (_utils_js__WEBPACK_IMPORTED_MODULE_3__.isServer ? 0 : 3);\n      const retryDelay = config.retryDelay ?? defaultRetryDelay;\n      const delay = typeof retryDelay === \"function\" ? retryDelay(failureCount, error) : retryDelay;\n      const shouldRetry = retry === true || typeof retry === \"number\" && failureCount < retry || typeof retry === \"function\" && retry(failureCount, error);\n      if (isRetryCancelled || !shouldRetry) {\n        reject(error);\n        return;\n      }\n      failureCount++;\n      config.onFail?.(failureCount, error);\n      (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.sleep)(delay).then(() => {\n        return canContinue() ? void 0 : pause();\n      }).then(() => {\n        if (isRetryCancelled) {\n          reject(error);\n        } else {\n          run();\n        }\n      });\n    });\n  };\n  return {\n    promise: thenable,\n    cancel,\n    continue: () => {\n      continueFn?.();\n      return thenable;\n    },\n    cancelRetry,\n    continueRetry,\n    canStart,\n    start: () => {\n      if (canStart()) {\n        run();\n      } else {\n        pause().then(run);\n      }\n      return thenable;\n    }\n  };\n}\n\n//# sourceMappingURL=retryer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/retryer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js":
/*!************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/subscribable.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Subscribable: () => (/* binding */ Subscribable)\n/* harmony export */ });\n// src/subscribable.ts\nvar Subscribable = class {\n  constructor() {\n    this.listeners = /* @__PURE__ */ new Set();\n    this.subscribe = this.subscribe.bind(this);\n  }\n  subscribe(listener) {\n    this.listeners.add(listener);\n    this.onSubscribe();\n    return () => {\n      this.listeners.delete(listener);\n      this.onUnsubscribe();\n    };\n  }\n  hasListeners() {\n    return this.listeners.size > 0;\n  }\n  onSubscribe() {\n  }\n  onUnsubscribe() {\n  }\n};\n\n//# sourceMappingURL=subscribable.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3F1ZXJ5LWNvcmUvYnVpbGQvbW9kZXJuL3N1YnNjcmliYWJsZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFHRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1sZXNzb24tcGxhbi1nZW5lcmF0b3IvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3F1ZXJ5LWNvcmUvYnVpbGQvbW9kZXJuL3N1YnNjcmliYWJsZS5qcz8zZTdmIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy9zdWJzY3JpYmFibGUudHNcbnZhciBTdWJzY3JpYmFibGUgPSBjbGFzcyB7XG4gIGNvbnN0cnVjdG9yKCkge1xuICAgIHRoaXMubGlzdGVuZXJzID0gLyogQF9fUFVSRV9fICovIG5ldyBTZXQoKTtcbiAgICB0aGlzLnN1YnNjcmliZSA9IHRoaXMuc3Vic2NyaWJlLmJpbmQodGhpcyk7XG4gIH1cbiAgc3Vic2NyaWJlKGxpc3RlbmVyKSB7XG4gICAgdGhpcy5saXN0ZW5lcnMuYWRkKGxpc3RlbmVyKTtcbiAgICB0aGlzLm9uU3Vic2NyaWJlKCk7XG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIHRoaXMubGlzdGVuZXJzLmRlbGV0ZShsaXN0ZW5lcik7XG4gICAgICB0aGlzLm9uVW5zdWJzY3JpYmUoKTtcbiAgICB9O1xuICB9XG4gIGhhc0xpc3RlbmVycygpIHtcbiAgICByZXR1cm4gdGhpcy5saXN0ZW5lcnMuc2l6ZSA+IDA7XG4gIH1cbiAgb25TdWJzY3JpYmUoKSB7XG4gIH1cbiAgb25VbnN1YnNjcmliZSgpIHtcbiAgfVxufTtcbmV4cG9ydCB7XG4gIFN1YnNjcmliYWJsZVxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXN1YnNjcmliYWJsZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/thenable.js":
/*!********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/thenable.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pendingThenable: () => (/* binding */ pendingThenable)\n/* harmony export */ });\n// src/thenable.ts\nfunction pendingThenable() {\n  let resolve;\n  let reject;\n  const thenable = new Promise((_resolve, _reject) => {\n    resolve = _resolve;\n    reject = _reject;\n  });\n  thenable.status = \"pending\";\n  thenable.catch(() => {\n  });\n  function finalize(data) {\n    Object.assign(thenable, data);\n    delete thenable.resolve;\n    delete thenable.reject;\n  }\n  thenable.resolve = (value) => {\n    finalize({\n      status: \"fulfilled\",\n      value\n    });\n    resolve(value);\n  };\n  thenable.reject = (reason) => {\n    finalize({\n      status: \"rejected\",\n      reason\n    });\n    reject(reason);\n  };\n  return thenable;\n}\n\n//# sourceMappingURL=thenable.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3F1ZXJ5LWNvcmUvYnVpbGQvbW9kZXJuL3RoZW5hYmxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFHRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1sZXNzb24tcGxhbi1nZW5lcmF0b3IvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3F1ZXJ5LWNvcmUvYnVpbGQvbW9kZXJuL3RoZW5hYmxlLmpzP2Y0ODciXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL3RoZW5hYmxlLnRzXG5mdW5jdGlvbiBwZW5kaW5nVGhlbmFibGUoKSB7XG4gIGxldCByZXNvbHZlO1xuICBsZXQgcmVqZWN0O1xuICBjb25zdCB0aGVuYWJsZSA9IG5ldyBQcm9taXNlKChfcmVzb2x2ZSwgX3JlamVjdCkgPT4ge1xuICAgIHJlc29sdmUgPSBfcmVzb2x2ZTtcbiAgICByZWplY3QgPSBfcmVqZWN0O1xuICB9KTtcbiAgdGhlbmFibGUuc3RhdHVzID0gXCJwZW5kaW5nXCI7XG4gIHRoZW5hYmxlLmNhdGNoKCgpID0+IHtcbiAgfSk7XG4gIGZ1bmN0aW9uIGZpbmFsaXplKGRhdGEpIHtcbiAgICBPYmplY3QuYXNzaWduKHRoZW5hYmxlLCBkYXRhKTtcbiAgICBkZWxldGUgdGhlbmFibGUucmVzb2x2ZTtcbiAgICBkZWxldGUgdGhlbmFibGUucmVqZWN0O1xuICB9XG4gIHRoZW5hYmxlLnJlc29sdmUgPSAodmFsdWUpID0+IHtcbiAgICBmaW5hbGl6ZSh7XG4gICAgICBzdGF0dXM6IFwiZnVsZmlsbGVkXCIsXG4gICAgICB2YWx1ZVxuICAgIH0pO1xuICAgIHJlc29sdmUodmFsdWUpO1xuICB9O1xuICB0aGVuYWJsZS5yZWplY3QgPSAocmVhc29uKSA9PiB7XG4gICAgZmluYWxpemUoe1xuICAgICAgc3RhdHVzOiBcInJlamVjdGVkXCIsXG4gICAgICByZWFzb25cbiAgICB9KTtcbiAgICByZWplY3QocmVhc29uKTtcbiAgfTtcbiAgcmV0dXJuIHRoZW5hYmxlO1xufVxuZXhwb3J0IHtcbiAgcGVuZGluZ1RoZW5hYmxlXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dGhlbmFibGUuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/thenable.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/utils.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addToEnd: () => (/* binding */ addToEnd),\n/* harmony export */   addToStart: () => (/* binding */ addToStart),\n/* harmony export */   ensureQueryFn: () => (/* binding */ ensureQueryFn),\n/* harmony export */   functionalUpdate: () => (/* binding */ functionalUpdate),\n/* harmony export */   hashKey: () => (/* binding */ hashKey),\n/* harmony export */   hashQueryKeyByOptions: () => (/* binding */ hashQueryKeyByOptions),\n/* harmony export */   isPlainArray: () => (/* binding */ isPlainArray),\n/* harmony export */   isPlainObject: () => (/* binding */ isPlainObject),\n/* harmony export */   isServer: () => (/* binding */ isServer),\n/* harmony export */   isValidTimeout: () => (/* binding */ isValidTimeout),\n/* harmony export */   keepPreviousData: () => (/* binding */ keepPreviousData),\n/* harmony export */   matchMutation: () => (/* binding */ matchMutation),\n/* harmony export */   matchQuery: () => (/* binding */ matchQuery),\n/* harmony export */   noop: () => (/* binding */ noop),\n/* harmony export */   partialMatchKey: () => (/* binding */ partialMatchKey),\n/* harmony export */   replaceData: () => (/* binding */ replaceData),\n/* harmony export */   replaceEqualDeep: () => (/* binding */ replaceEqualDeep),\n/* harmony export */   resolveEnabled: () => (/* binding */ resolveEnabled),\n/* harmony export */   resolveStaleTime: () => (/* binding */ resolveStaleTime),\n/* harmony export */   shallowEqualObjects: () => (/* binding */ shallowEqualObjects),\n/* harmony export */   skipToken: () => (/* binding */ skipToken),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   timeUntilStale: () => (/* binding */ timeUntilStale)\n/* harmony export */ });\n// src/utils.ts\nvar isServer = typeof window === \"undefined\" || \"Deno\" in globalThis;\nfunction noop() {\n  return void 0;\n}\nfunction functionalUpdate(updater, input) {\n  return typeof updater === \"function\" ? updater(input) : updater;\n}\nfunction isValidTimeout(value) {\n  return typeof value === \"number\" && value >= 0 && value !== Infinity;\n}\nfunction timeUntilStale(updatedAt, staleTime) {\n  return Math.max(updatedAt + (staleTime || 0) - Date.now(), 0);\n}\nfunction resolveStaleTime(staleTime, query) {\n  return typeof staleTime === \"function\" ? staleTime(query) : staleTime;\n}\nfunction resolveEnabled(enabled, query) {\n  return typeof enabled === \"function\" ? enabled(query) : enabled;\n}\nfunction matchQuery(filters, query) {\n  const {\n    type = \"all\",\n    exact,\n    fetchStatus,\n    predicate,\n    queryKey,\n    stale\n  } = filters;\n  if (queryKey) {\n    if (exact) {\n      if (query.queryHash !== hashQueryKeyByOptions(queryKey, query.options)) {\n        return false;\n      }\n    } else if (!partialMatchKey(query.queryKey, queryKey)) {\n      return false;\n    }\n  }\n  if (type !== \"all\") {\n    const isActive = query.isActive();\n    if (type === \"active\" && !isActive) {\n      return false;\n    }\n    if (type === \"inactive\" && isActive) {\n      return false;\n    }\n  }\n  if (typeof stale === \"boolean\" && query.isStale() !== stale) {\n    return false;\n  }\n  if (fetchStatus && fetchStatus !== query.state.fetchStatus) {\n    return false;\n  }\n  if (predicate && !predicate(query)) {\n    return false;\n  }\n  return true;\n}\nfunction matchMutation(filters, mutation) {\n  const { exact, status, predicate, mutationKey } = filters;\n  if (mutationKey) {\n    if (!mutation.options.mutationKey) {\n      return false;\n    }\n    if (exact) {\n      if (hashKey(mutation.options.mutationKey) !== hashKey(mutationKey)) {\n        return false;\n      }\n    } else if (!partialMatchKey(mutation.options.mutationKey, mutationKey)) {\n      return false;\n    }\n  }\n  if (status && mutation.state.status !== status) {\n    return false;\n  }\n  if (predicate && !predicate(mutation)) {\n    return false;\n  }\n  return true;\n}\nfunction hashQueryKeyByOptions(queryKey, options) {\n  const hashFn = options?.queryKeyHashFn || hashKey;\n  return hashFn(queryKey);\n}\nfunction hashKey(queryKey) {\n  return JSON.stringify(\n    queryKey,\n    (_, val) => isPlainObject(val) ? Object.keys(val).sort().reduce((result, key) => {\n      result[key] = val[key];\n      return result;\n    }, {}) : val\n  );\n}\nfunction partialMatchKey(a, b) {\n  if (a === b) {\n    return true;\n  }\n  if (typeof a !== typeof b) {\n    return false;\n  }\n  if (a && b && typeof a === \"object\" && typeof b === \"object\") {\n    return !Object.keys(b).some((key) => !partialMatchKey(a[key], b[key]));\n  }\n  return false;\n}\nfunction replaceEqualDeep(a, b) {\n  if (a === b) {\n    return a;\n  }\n  const array = isPlainArray(a) && isPlainArray(b);\n  if (array || isPlainObject(a) && isPlainObject(b)) {\n    const aItems = array ? a : Object.keys(a);\n    const aSize = aItems.length;\n    const bItems = array ? b : Object.keys(b);\n    const bSize = bItems.length;\n    const copy = array ? [] : {};\n    let equalItems = 0;\n    for (let i = 0; i < bSize; i++) {\n      const key = array ? i : bItems[i];\n      if ((!array && aItems.includes(key) || array) && a[key] === void 0 && b[key] === void 0) {\n        copy[key] = void 0;\n        equalItems++;\n      } else {\n        copy[key] = replaceEqualDeep(a[key], b[key]);\n        if (copy[key] === a[key] && a[key] !== void 0) {\n          equalItems++;\n        }\n      }\n    }\n    return aSize === bSize && equalItems === aSize ? a : copy;\n  }\n  return b;\n}\nfunction shallowEqualObjects(a, b) {\n  if (!b || Object.keys(a).length !== Object.keys(b).length) {\n    return false;\n  }\n  for (const key in a) {\n    if (a[key] !== b[key]) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction isPlainArray(value) {\n  return Array.isArray(value) && value.length === Object.keys(value).length;\n}\nfunction isPlainObject(o) {\n  if (!hasObjectPrototype(o)) {\n    return false;\n  }\n  const ctor = o.constructor;\n  if (ctor === void 0) {\n    return true;\n  }\n  const prot = ctor.prototype;\n  if (!hasObjectPrototype(prot)) {\n    return false;\n  }\n  if (!prot.hasOwnProperty(\"isPrototypeOf\")) {\n    return false;\n  }\n  if (Object.getPrototypeOf(o) !== Object.prototype) {\n    return false;\n  }\n  return true;\n}\nfunction hasObjectPrototype(o) {\n  return Object.prototype.toString.call(o) === \"[object Object]\";\n}\nfunction sleep(timeout) {\n  return new Promise((resolve) => {\n    setTimeout(resolve, timeout);\n  });\n}\nfunction replaceData(prevData, data, options) {\n  if (typeof options.structuralSharing === \"function\") {\n    return options.structuralSharing(prevData, data);\n  } else if (options.structuralSharing !== false) {\n    if (true) {\n      try {\n        return replaceEqualDeep(prevData, data);\n      } catch (error) {\n        console.error(\n          `Structural sharing requires data to be JSON serializable. To fix this, turn off structuralSharing or return JSON-serializable data from your queryFn. [${options.queryHash}]: ${error}`\n        );\n      }\n    }\n    return replaceEqualDeep(prevData, data);\n  }\n  return data;\n}\nfunction keepPreviousData(previousData) {\n  return previousData;\n}\nfunction addToEnd(items, item, max = 0) {\n  const newItems = [...items, item];\n  return max && newItems.length > max ? newItems.slice(1) : newItems;\n}\nfunction addToStart(items, item, max = 0) {\n  const newItems = [item, ...items];\n  return max && newItems.length > max ? newItems.slice(0, -1) : newItems;\n}\nvar skipToken = Symbol();\nfunction ensureQueryFn(options, fetchOptions) {\n  if (true) {\n    if (options.queryFn === skipToken) {\n      console.error(\n        `Attempted to invoke queryFn when set to skipToken. This is likely a configuration error. Query hash: '${options.queryHash}'`\n      );\n    }\n  }\n  if (!options.queryFn && fetchOptions?.initialPromise) {\n    return () => fetchOptions.initialPromise;\n  }\n  if (!options.queryFn || options.queryFn === skipToken) {\n    return () => Promise.reject(new Error(`Missing queryFn: '${options.queryHash}'`));\n  }\n  return options.queryFn;\n}\n\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryClientContext: () => (/* binding */ QueryClientContext),\n/* harmony export */   QueryClientProvider: () => (/* binding */ QueryClientProvider),\n/* harmony export */   useQueryClient: () => (/* binding */ useQueryClient)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ QueryClientContext,QueryClientProvider,useQueryClient auto */ // src/QueryClientProvider.tsx\n\n\nvar QueryClientContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(void 0);\nvar useQueryClient = (queryClient)=>{\n    const client = react__WEBPACK_IMPORTED_MODULE_0__.useContext(QueryClientContext);\n    if (queryClient) {\n        return queryClient;\n    }\n    if (!client) {\n        throw new Error(\"No QueryClient set, use QueryClientProvider to set one\");\n    }\n    return client;\n};\nvar QueryClientProvider = ({ client, children })=>{\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        client.mount();\n        return ()=>{\n            client.unmount();\n        };\n    }, [\n        client\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(QueryClientContext.Provider, {\n        value: client,\n        children\n    });\n};\n //# sourceMappingURL=QueryClientProvider.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\n");

/***/ })

};
;