import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from enhanced_parser import EnhancedSchemeParser
    print("✓ EnhancedSchemeParser imported successfully")
    
    from cbc_lesson_plan_generator import CBCLessonPlanGenerator
    print("✓ CBCLessonPlanGenerator imported successfully")
    
    # Test parsing
    with open('../grade-9-rationalized-pre-technical-schemes-of-work-term-2.pdf', 'rb') as f:
        content = f.read()
    
    parser = EnhancedSchemeParser()
    result = parser.parse_scheme(content, 'test.pdf')
    
    print(f"✓ Parsing successful: {result['success']}")
    print(f"✓ Generated {len(result['lesson_plans'])} lesson plans")
    
    if result['lesson_plans']:
        first_plan = result['lesson_plans'][0]
        print(f"✓ First plan has proper CBC format:")
        print(f"  - School: {first_plan.get('school', 'N/A')}")
        print(f"  - Level: {first_plan.get('level', 'N/A')}")
        print(f"  - Learning Area: {first_plan.get('learning_area', 'N/A')}")
        print(f"  - Strand: {first_plan.get('strand', 'N/A')}")
        print(f"  - Sub-strand: {first_plan.get('sub_strand', 'N/A')}")
        print(f"  - Learning outcomes: {len(first_plan.get('specific_learning_outcomes', []))}")
        
except Exception as e:
    print(f"✗ Error: {e}")
    import traceback
    traceback.print_exc()
