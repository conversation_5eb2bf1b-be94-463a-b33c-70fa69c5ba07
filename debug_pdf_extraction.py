"""
Debug PDF Extraction - Test STM2025.pdf processing
"""

import sys
import os

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from optimized_file_processor import OptimizedFileProcessor

def test_pdf_extraction():
    """Test PDF extraction with STM2025.pdf"""
    
    print("🔧 Debugging PDF Extraction")
    print("=" * 50)
    
    # Check if STM2025.pdf exists
    pdf_path = "STM2025.pdf"
    if not os.path.exists(pdf_path):
        print(f"❌ STM2025.pdf not found in current directory")
        print(f"Current directory: {os.getcwd()}")
        print("Available files:")
        for file in os.listdir("."):
            if file.endswith(".pdf"):
                print(f"  📄 {file}")
        return
    
    print(f"✅ Found STM2025.pdf")
    
    # Read the PDF file
    try:
        with open(pdf_path, 'rb') as f:
            file_content = f.read()
        
        print(f"📁 File size: {len(file_content)} bytes")
        
        # Test with optimized processor
        processor = OptimizedFileProcessor()
        
        print("🚀 Testing optimized PDF extraction...")
        text, result = processor.extract_text_fast(file_content, "STM2025.pdf", "debug_test")
        
        print(f"✅ Extraction result:")
        print(f"   Success: {result.get('success', False)}")
        print(f"   Text length: {len(text)} characters")
        print(f"   Pages processed: {result.get('pages_processed', 0)}")
        print(f"   Total pages: {result.get('total_pages', 0)}")
        
        if result.get('error'):
            print(f"❌ Error: {result['error']}")
        
        if text:
            print(f"\n📝 First 500 characters:")
            print("-" * 50)
            print(text[:500])
            print("-" * 50)
        else:
            print("❌ No text extracted!")
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        import traceback
        traceback.print_exc()

def test_direct_pymupdf():
    """Test direct PyMuPDF extraction"""

    print("\n🔍 Testing Direct PyMuPDF")
    print("=" * 50)

    try:
        import fitz

        pdf_path = "STM2025.pdf"
        if not os.path.exists(pdf_path):
            print("❌ STM2025.pdf not found")
            return

        with open(pdf_path, 'rb') as f:
            file_content = f.read()

        print(f"📁 File size: {len(file_content)} bytes")

        # Direct PyMuPDF test
        doc = fitz.open(stream=file_content, filetype="pdf")
        print(f"📄 Total pages: {len(doc)}")

        if len(doc) > 0:
            page = doc[0]
            page_text = page.get_text("text")
            print(f"📝 First page text length: {len(page_text)} characters")

            if page_text.strip():
                print(f"\n📝 First 300 characters from page 1:")
                print("-" * 50)
                print(page_text[:300])
                print("-" * 50)
            else:
                print("❌ First page has no text!")

        doc.close()

    except Exception as e:
        print(f"❌ Direct PyMuPDF failed: {str(e)}")
        import traceback
        traceback.print_exc()

def test_pdfplumber():
    """Test pdfplumber extraction"""

    print("\n🔧 Testing pdfplumber")
    print("=" * 50)

    try:
        import pdfplumber

        pdf_path = "STM2025.pdf"
        if not os.path.exists(pdf_path):
            print("❌ STM2025.pdf not found")
            return

        with open(pdf_path, 'rb') as f:
            file_content = f.read()

        print(f"📁 File size: {len(file_content)} bytes")

        # Test pdfplumber
        import io
        pdf_stream = io.BytesIO(file_content)

        with pdfplumber.open(pdf_stream) as pdf:
            print(f"📄 Total pages: {len(pdf.pages)}")

            if len(pdf.pages) > 0:
                page = pdf.pages[0]
                page_text = page.extract_text()

                if page_text:
                    print(f"📝 First page text length: {len(page_text)} characters")
                    print(f"\n📝 First 500 characters from page 1:")
                    print("-" * 50)
                    print(page_text[:500])
                    print("-" * 50)
                else:
                    print("❌ First page has no text!")

    except Exception as e:
        print(f"❌ pdfplumber failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🧪 PDF Extraction Debug Tool")
    print("Testing STM2025.pdf extraction...")
    print()
    
    # Run tests
    test_pdf_extraction()
    test_direct_pymupdf()
    test_pdfplumber()

    print("\n✅ Debug completed!")
    print("Check the results above to identify PDF extraction issues.")
